#!/usr/bin/env python3
"""
Test Session Management Script
Run this to test the improved session management features
"""

import asyncio
import os
from telegram_automation_tool.config import load_config
from telegram_automation_tool.session_manager import SessionManager

async def test_session_management():
    """Test session management features"""
    print("🧪 Testing Session Management")
    print("=" * 40)
    
    try:
        # Load config
        config = load_config()
        print(f"✅ Config loaded - API ID: {config.get('api_id', 'Not set')}")
        
        # Create session manager
        session_manager = SessionManager(config)
        
        # List existing sessions
        sessions = session_manager.get_session_files()
        print(f"\n📋 Found {len(sessions)} existing sessions:")
        for session in sessions:
            print(f"  - {session}")
        
        if not sessions:
            print("\n⚠️  No sessions found. Please create a session first using:")
            print("   1. GUI: python main.py -> Sessions tab -> Add Session")
            print("   2. Command line: python test_session_creation.py")
            return
        
        # Test session validation
        print(f"\n🔍 Testing session validation...")
        for session in sessions[:1]:  # Test first session
            print(f"Validating session: {session}")
            try:
                info = await session_manager.validate_session(session)
                print(f"  Status: {info['status']}")
                if info['status'] == 'valid':
                    user = info.get('user', {})
                    print(f"  User: {user.get('first_name', 'N/A')} {user.get('last_name', 'N/A')}")
                    print(f"  Username: @{user.get('username', 'N/A')}")
                    print(f"  Phone: {user.get('phone', 'N/A')}")
                elif info['status'] == 'error':
                    print(f"  Error: {info.get('error', 'Unknown error')}")
                    print("  This session may need to be recreated or verified")
            except Exception as e:
                print(f"  Error validating session: {e}")
        
        # Test resend code functionality (if session exists)
        if sessions:
            session = sessions[0]
            print(f"\n📱 Testing resend code for session: {session}")
            
            # Extract phone number from session name
            if session.startswith("session_"):
                phone = session.replace("session_", "")
                if not phone.startswith("+"):
                    phone = "+" + phone
                print(f"  Extracted phone: {phone}")
                
                # Test resend code
                try:
                    result = await session_manager.resend_code_for_session(session, phone)
                    print(f"  Resend result: {result['status']}")
                    if result['status'] == 'code_sent':
                        print(f"  Phone code hash: {result.get('phone_code_hash', 'None')[:20]}...")
                        print("  ✅ Resend code functionality works!")
                    else:
                        print(f"  ❌ Resend failed: {result.get('error', 'Unknown error')}")
                except Exception as e:
                    print(f"  ❌ Error testing resend: {e}")
            else:
                print("  ⚠️  Cannot extract phone number from session name")
        
        print(f"\n🎉 Session management test completed!")
        print("\nNext steps:")
        print("1. Run the GUI: python main.py")
        print("2. Go to Sessions tab to see the improved interface")
        print("3. Try the new session management features:")
        print("   - Delete sessions")
        print("   - Rename sessions")
        print("   - Resend verification codes")
        print("   - Validate sessions")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_session_management())
