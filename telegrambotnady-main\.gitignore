# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
venv/**/*
env/
ENV/
.env

# Specifically exclude large PySide6 DLLs
**/Qt6WebEngineCore.dll
**/PySide6/Qt6*.dll

# Project specific
*.session
*.session-journal
logs/
output/
temp/
sessions/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# OS specific files
.DS_Store
Thumbs.db