{"api_id": ********, "api_hash": "your_api_hash_here", "sessions_dir": "sessions", "log_dir": "logs", "temp_dir": "temp", "output_dir": "output", "default_delay_min": 2.0, "default_delay_max": 5.0, "proxy": {"enabled": false, "type": "socks5", "host": "127.0.0.1", "port": 9050, "username": "", "password": ""}, "checker": {"input_file": "input_accounts.txt", "output_folder": "output/checker", "delay_between_checks": 1.0}, "forwarder": {"delay_min": 2.0, "delay_max": 5.0, "sessions_folder": "sessions"}, "leaver": {"sessions_folder": "sessions", "delay": 3}}