#!/usr/bin/env python3
"""
Telegram Automation Tool - Main Entry Point
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from telegram_automation_tool.app import TelegramAutomationApp

def main():
    """Main entry point for the Telegram Automation Tool"""
    try:
        app = TelegramAutomationApp()
        app.mainloop()
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
    except Exception as e:
        print(f"\nError starting application: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
