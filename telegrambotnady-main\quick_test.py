#!/usr/bin/env python3
"""
Quick test to verify the Telegram Automation Tool can start without errors
"""

import sys
import os

def test_imports():
    """Test if all critical imports work"""
    try:
        from telegram_automation_tool.config import load_config
        from telegram_automation_tool.logger import setup_logger
        from telegram_automation_tool.session_manager import SessionManager
        print("✅ All imports successful")
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_config():
    """Test if configuration loads properly"""
    try:
        from telegram_automation_tool.config import load_config
        config = load_config()
        print(f"✅ Configuration loaded - API ID: {config.get('api_id', 'Not set')}")
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_app_creation():
    """Test if the main app can be created"""
    try:
        from telegram_automation_tool.app import TelegramAutomationApp
        print("✅ App class can be imported")
        return True
    except Exception as e:
        print(f"❌ App creation error: {e}")
        return False

def main():
    """Run all tests"""
    print("Quick Test - Telegram Automation Tool")
    print("=" * 40)
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_config),
        ("App Creation", test_app_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\nTesting {test_name}...")
        if test_func():
            passed += 1
    
    print(f"\n{'='*40}")
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The tool is ready to use.")
        print("\nTo start the application, run:")
        print("  python main.py")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
