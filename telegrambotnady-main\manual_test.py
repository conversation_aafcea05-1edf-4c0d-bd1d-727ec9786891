#!/usr/bin/env python3
"""
Manual Test Script for Telegram Automation Tool
Run this to test basic functionality without the GUI
"""

import asyncio
import os
import sys
from telegram_automation_tool.config import load_config
from telegram_automation_tool.session_manager import SessionManager
from telegram_automation_tool.checker import Checker

async def test_config():
    """Test configuration loading"""
    print("🔧 Testing Configuration...")
    try:
        config = load_config()
        print(f"✅ Config loaded - API ID: {config.get('api_id', 'Not set')}")
        print(f"✅ API Hash: {'Set' if config.get('api_hash') else 'Not set'}")
        return config
    except Exception as e:
        print(f"❌ Config error: {e}")
        return None

async def test_session_manager(config):
    """Test session manager"""
    print("\n🔑 Testing Session Manager...")
    try:
        session_manager = SessionManager(config)
        sessions = session_manager.get_session_files()
        print(f"✅ Found {len(sessions)} sessions: {sessions}")
        return session_manager
    except Exception as e:
        print(f"❌ Session manager error: {e}")
        return None

async def test_checker(config, session_name):
    """Test checker functionality"""
    print(f"\n🔍 Testing Checker with session: {session_name}")
    try:
        checker = Checker(config)
        
        # Test with a known public account
        test_identifiers = ["@telegram", "@durov"]
        
        async def progress_callback(current, total, identifier):
            print(f"   Checking {identifier}... ({current}/{total})")
        
        results = await checker.run(test_identifiers, session_name, progress_callback)
        
        print(f"✅ Checker completed - Results:")
        for identifier, result in results.items():
            status = result.get('status', 'Unknown')
            print(f"   {identifier}: {status}")
        
        return results
    except Exception as e:
        print(f"❌ Checker error: {e}")
        return None

async def test_directories():
    """Test directory creation"""
    print("\n📁 Testing Directories...")
    directories = ['sessions', 'logs', 'output', 'output/checker', 'output/forwarder', 'output/leaver']
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ {directory}/ - OK")
        except Exception as e:
            print(f"❌ {directory}/ - Error: {e}")

def main():
    """Run all tests"""
    print("🧪 Manual Test - Telegram Automation Tool")
    print("=" * 50)
    
    # Test directories
    asyncio.run(test_directories())
    
    # Test configuration
    config = asyncio.run(test_config())
    if not config:
        print("\n❌ Configuration test failed. Please check your config.json")
        return
    
    # Test session manager
    session_manager = asyncio.run(test_session_manager(config))
    if not session_manager:
        print("\n❌ Session manager test failed.")
        return
    
    # Get available sessions
    sessions = session_manager.get_session_files()
    if not sessions:
        print("\n⚠️  No sessions found.")
        print("You can create a session using:")
        print("   1. GUI method: python main.py -> Sessions tab -> Add Session")
        print("   2. Command line: python test_session_creation.py")
        print("\nFor now, let's skip the checker test.")
        return
    
    # Test checker with first available session
    session_name = sessions[0]
    print(f"\n🎯 Using session: {session_name}")
    
    results = asyncio.run(test_checker(config, session_name))
    
    print("\n" + "=" * 50)
    if results:
        print("🎉 Manual test completed successfully!")
        print("\nNext steps:")
        print("1. Run the GUI: python main.py")
        print("2. Test each module manually")
        print("3. Check output files in the output/ directory")
    else:
        print("❌ Some tests failed. Check the errors above.")

if __name__ == "__main__":
    main()
