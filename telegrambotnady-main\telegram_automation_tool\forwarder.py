# telegram_automation_tool/forwarder.py

import os
import json
import random
import asyncio
import threading
from datetime import datetime
from typing import List, Optional, Dict, Any
from telethon import events
from telethon.errors import (
    FloodWaitError, 
    ChatWriteForbiddenError, 
    UserNotParticipantError,
    ChannelPrivateError,
    SessionPasswordNeededError
)
from telegram_automation_tool.session_manager import SessionManager
from telegram_automation_tool.logger import setup_logger

class Forwarder:
    def __init__(
        self,
        config,
        listener_session: str,
        sender_sessions: List[str],
        source: str,
        targets: List[str],
        remove_sender_tag: bool = False,
        delay_min: float = 2.0,
        delay_max: float = 5.0,
        cancel_event: Optional[threading.Event] = None,
        logger=None
    ):
        self.config = config
        self.listener_session = listener_session
        self.sender_sessions = sender_sessions
        self.source = source
        self.targets = targets
        self.remove_sender_tag = remove_sender_tag
        self.delay_min = delay_min
        self.delay_max = delay_max
        self._cancel_event = cancel_event
        self.logger = logger or setup_logger('forwarder', 'logs/forwarder.log')
        self.session_manager = SessionManager(config)

        # Clients
        self._listener_client = None
        self._sender_clients = []
        self._sender_index = 0
        
        # Statistics
        self.results = {
            "forwarded": 0, 
            "errors": 0,
            "skipped": 0,
            "start_time": None,
            "end_time": None,
            "details": []
        }
        
        # Rate limiting
        self.rate_limits = {
            'messages_per_hour': 50,
            'messages_per_session': 20
        }
        
        self._running = False
        self._message_count = 0
        self._session_message_counts = {}

    def _next_sender(self):
        """Get next sender client with rotation"""
        if not self._sender_clients:
            return None
            
        client = self._sender_clients[self._sender_index]
        self._sender_index = (self._sender_index + 1) % len(self._sender_clients)
        return client

    async def _download_media(self, client, message):
        """Download media file for forwarding without sender tag"""
        try:
            temp_dir = self.config.get("temp_dir", "temp")
            os.makedirs(temp_dir, exist_ok=True)
            
            file_path = await client.download_media(
                message, 
                file=os.path.join(temp_dir, f"{message.id}_{random.randint(1000, 9999)}")
            )
            return file_path
        except Exception as e:
            self.logger.error(f"Error downloading media: {e}")
            return None

    async def _check_rate_limit(self, session_name):
        """Check if session has hit rate limit"""
        current_hour = datetime.now().hour
        key = f"{session_name}_{current_hour}"
        
        if key not in self._session_message_counts:
            self._session_message_counts[key] = 0
            
        if self._session_message_counts[key] >= self.rate_limits['messages_per_session']:
            return False
            
        self._session_message_counts[key] += 1
        return True

    async def _handle_message(self, event):
        """Handle incoming message from source"""
        if self._cancel_event and self._cancel_event.is_set():
            raise asyncio.CancelledError("Forwarder task cancelled by user")

        msg = event.message
        self.logger.info(f"📨 Received message {msg.id} from {self.source}")
        
        try:
            for target in self.targets:
                if self._cancel_event and self._cancel_event.is_set():
                    raise asyncio.CancelledError("Forwarder task cancelled by user")

                sender_client = self._next_sender()
                if not sender_client:
                    self.logger.error("No sender clients available")
                    continue
                
                # Check rate limit
                session_name = getattr(sender_client, '_session_name', 'unknown')
                if not await self._check_rate_limit(session_name):
                    self.logger.warning(f"Rate limit reached for session {session_name}")
                    continue

                # Random delay
                delay = random.uniform(self.delay_min, self.delay_max)
                await asyncio.sleep(delay)

                try:
                    # Validate target entity exists
                    try:
                        target_entity = await sender_client.get_entity(int(target) if str(target).isdigit() else target)
                        target_name = getattr(target_entity, 'title', target)
                    except ValueError as e:
                        self.logger.error(f"❌ Target entity not found: {target} - {e}")
                        self.results["errors"] += 1
                        self.results["details"].append({
                            "message_id": msg.id,
                            "target": target,
                            "session": session_name,
                            "status": "error",
                            "error": f"Target entity not found: {e}",
                            "timestamp": datetime.now().isoformat()
                        })
                        continue
                    except Exception as e:
                        self.logger.error(f"❌ Error resolving target entity: {target} - {e}")
                        self.results["errors"] += 1
                        self.results["details"].append({
                            "message_id": msg.id,
                            "target": target,
                            "session": session_name,
                            "status": "error",
                            "error": f"Error resolving target entity: {e}",
                            "timestamp": datetime.now().isoformat()
                        })
                        continue
                        
                    if not self.remove_sender_tag:
                        # Forward with sender tag
                        await sender_client.forward_messages(target_entity, msg)
                        self.logger.info(f"✅ Forwarded message {msg.id} to {target_name}")
                    else:
                        # Forward without sender tag
                        if msg.media:
                            # Download and re-upload media
                            file_path = await self._download_media(self._listener_client, msg)
                            if file_path:
                                try:
                                    await sender_client.send_file(
                                        target_entity, 
                                        file_path, 
                                        caption=msg.message or ""
                                    )
                                    self.logger.info(f"✅ Sent media {msg.id} to {target_name} (no sender tag)")
                                finally:
                                    # Clean up temp file
                                    try:
                                        os.remove(file_path)
                                    except:
                                        pass
                            else:
                                # Fallback to regular forward
                                await sender_client.forward_messages(target_entity, msg)
                                self.logger.info(f"✅ Forwarded media {msg.id} to {target_name} (fallback)")
                        else:
                            # Text message
                            await sender_client.send_message(target_entity, msg.message or "")
                            self.logger.info(f"✅ Sent text {msg.id} to {target_name} (no sender tag)")
                    
                    self.results["forwarded"] += 1
                    self.results["details"].append({
                        "message_id": msg.id,
                        "target": target,
                        "session": session_name,
                        "status": "success",
                        "timestamp": datetime.now().isoformat()
                    })
                    
                except FloodWaitError as e:
                    self.logger.error(f"⏳ Flood wait for {e.seconds} seconds on session {session_name}")
                    self.results["errors"] += 1
                    self.results["details"].append({
                        "message_id": msg.id,
                        "target": target,
                        "session": session_name,
                        "status": "flood_wait",
                        "error": f"Flood wait: {e.seconds}s",
                        "timestamp": datetime.now().isoformat()
                    })
                    # Wait for flood wait to expire
                    await asyncio.sleep(e.seconds)
                    
                except (ChatWriteForbiddenError, UserNotParticipantError, ChannelPrivateError) as e:
                    self.logger.error(f"❌ Cannot send to {target}: {e}")
                    self.results["errors"] += 1
                    self.results["details"].append({
                        "message_id": msg.id,
                        "target": target,
                        "session": session_name,
                        "status": "forbidden",
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    })
                    
                except Exception as e:
                    self.logger.exception(f"❌ Error forwarding msg {msg.id} to {target}: {e}")
                    self.results["errors"] += 1
                    self.results["details"].append({
                        "message_id": msg.id,
                        "target": target,
                        "session": session_name,
                        "status": "error",
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    })
                    
        except asyncio.CancelledError:
            self.logger.info("Forwarder task cancelled by user")
            raise
        except Exception as e:
            self.logger.exception("Unhandled error in _handle_message: %s", e)

    async def start(self):
        """Start the forwarder"""
        if self._running:
            self.logger.warning("Forwarder already running")
            return

        self.results["start_time"] = datetime.now().isoformat()
        self.logger.info("🚀 Starting forwarder...")

        try:
            # Initialize listener client
            self._listener_client = self.session_manager.create_client(self.listener_session)
            self._listener_client._session_name = self.listener_session
            await self._listener_client.start()
            self.logger.info(f"✅ Listener client started: {self.listener_session}")

            # Initialize sender clients
            self._sender_clients = []
            for session_name in self.sender_sessions:
                try:
                    client = self.session_manager.create_client(session_name)
                    client._session_name = session_name
                    await client.start()
                    self._sender_clients.append(client)
                    self.logger.info(f"✅ Sender client started: {session_name}")
                except Exception as e:
                    self.logger.error(f"❌ Failed to start sender client {session_name}: {e}")

            if not self._sender_clients:
                raise Exception("No sender clients could be started")
                
            # Validate source entity exists
            try:
                source_entity = await self._listener_client.get_entity(self.source)
                self.logger.info(f"✅ Source entity found: {getattr(source_entity, 'title', self.source)}")
            except ValueError as e:
                self.logger.error(f"❌ Source entity not found: {self.source} - {e}")
                self.results["errors"] += 1
                self.results["details"].append({
                    "source": self.source,
                    "status": "error",
                    "error": f"Source entity not found: {e}",
                    "timestamp": datetime.now().isoformat()
                })
                await self.stop()
                return

            self._running = True
            self.logger.info(f"🎯 Forwarder started - Listening to {self.source}, sending to {len(self.targets)} targets")

            # Set up event handler
            async def handler(event):
                await self._handle_message(event)

            self._listener_client.add_event_handler(
                handler, 
                events.NewMessage(chats=self.source)
            )

            # Keep running until cancelled
            while not (self._cancel_event and self._cancel_event.is_set()):
                await asyncio.sleep(1)
                
        except asyncio.CancelledError:
            self.logger.info("Forwarder main loop cancelled")
        except Exception as e:
            self.logger.exception(f"Error in forwarder: {e}")
        finally:
            await self.stop()

    async def stop(self):
        """Stop the forwarder"""
        if not self._running:
            return
            
        self.logger.info("🛑 Stopping forwarder...")
        
        if self._listener_client:
            try:
                await self._listener_client.disconnect()
                self.logger.info("✅ Listener client disconnected")
            except Exception as e:
                self.logger.error(f"Error disconnecting listener: {e}")
                
        for client in self._sender_clients:
            try:
                await client.disconnect()
                self.logger.info(f"✅ Sender client {getattr(client, '_session_name', 'unknown')} disconnected")
            except Exception as e:
                self.logger.error(f"Error disconnecting sender: {e}")
                
        self._running = False
        self.results["end_time"] = datetime.now().isoformat()
        self.logger.info("✅ Forwarder stopped")

    def save_report(self, session_name):
        """Save detailed report"""
        out_dir = self.config.get("output_dir", "output") + "/forwarder"
        os.makedirs(out_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_name = session_name.replace(" ", "_")
        out_path = os.path.join(out_dir, f"{safe_name}_forwarder_report_{timestamp}.json")
        
        with open(out_path, "w", encoding="utf-8") as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
            
        self.logger.info(f"📊 Forwarder report saved to {out_path}")
        return out_path

    def get_stats(self):
        """Get current statistics"""
        return {
            "running": self._running,
            "forwarded": self.results["forwarded"],
            "errors": self.results["errors"],
            "skipped": self.results["skipped"],
            "active_sessions": len(self._sender_clients)
        }
