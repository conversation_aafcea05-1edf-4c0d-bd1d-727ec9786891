# 🧪 Manual Testing Checklist

## ✅ **Pre-Testing Setup**

- [ ] API credentials configured in config.json
- [ ] At least one session created
- [ ] All directories exist (sessions/, logs/, output/)
- [ ] Application starts without errors

## 🔧 **Configuration Testing**

### Settings Tab
- [ ] API ID and API Hash are displayed correctly
- [ ] "Save Settings" button works
- [ ] Configuration is saved to config.json
- [ ] Dark mode toggle works

### File Menu
- [ ] "Load Config" opens file dialog
- [ ] "Save Config" saves current configuration
- [ ] "Reload Sessions" refreshes session list
- [ ] "Exit" closes application properly

## 🔑 **Session Management Testing**

### Sessions Tab
- [ ] Existing sessions are displayed in list
- [ ] Session selection shows session info
- [ ] "Add Session" prompts for phone number
- [ ] Phone verification process works
- [ ] New session appears in list after creation
- [ ] "Refresh" button updates session list

## 🔍 **Checker Module Testing**

### Basic Functionality
- [ ] Identifiers field accepts input
- [ ] Session dropdown shows available sessions
- [ ] "Start Checking" button works
- [ ] Progress bar updates during checking
- [ ] Log shows checking progress
- [ ] "Stop" button stops the process
- [ ] Results are saved to output/checker/

### Test Cases
- [ ] Check single username (e.g., @telegram)
- [ ] Check multiple usernames (comma separated)
- [ ] Check phone number format
- [ ] Check invalid/non-existent accounts
- [ ] Verify JSON output format
- [ ] Verify CSV output format

## 📤 **Forwarder Module Testing**

### Configuration
- [ ] Listener session dropdown works
- [ ] Source field accepts group/channel IDs
- [ ] Sender sessions field accepts multiple sessions
- [ ] Targets field accepts multiple targets
- [ ] All fields are validated before starting

### Functionality
- [ ] "Start Forwarding" begins the process
- [ ] Log shows forwarding activity
- [ ] Messages are actually forwarded
- [ ] "Stop" button stops forwarding
- [ ] Error handling for invalid sources/targets
- [ ] Session rotation works (if multiple sessions)

### Test Cases
- [ ] Forward from public group to private group
- [ ] Forward text messages
- [ ] Forward media messages (images, videos)
- [ ] Forward files
- [ ] Test with multiple sender sessions
- [ ] Test error handling (blocked, invalid targets)

## 🚪 **Leaver Module Testing**

### Configuration
- [ ] Session dropdown works
- [ ] Mode selection (specific/all/inactive) works
- [ ] Specific targets field accepts input
- [ ] Days threshold for inactive mode works

### Functionality
- [ ] "Start Leaving" begins the process
- [ ] Log shows leaving activity
- [ ] Groups are actually left
- [ ] "Stop" button stops the process
- [ ] Error handling for invalid groups

### Test Cases
- [ ] Leave specific group (use test group)
- [ ] Leave all groups (be careful!)
- [ ] Leave inactive groups only
- [ ] Test with invalid group IDs
- [ ] Verify error handling

## 📊 **Output and Logging Testing**

### File Output
- [ ] Checker results saved to output/checker/
- [ ] Forwarder logs saved to output/forwarder/
- [ ] Leaver results saved to output/leaver/
- [ ] JSON files are valid and readable
- [ ] CSV files are valid and readable

### Application Logs
- [ ] Log files created in logs/ directory
- [ ] Logs contain detailed information
- [ ] Error logs are properly formatted
- [ ] Log rotation works (if implemented)

## 🎯 **Advanced Testing**

### Performance
- [ ] Application responds quickly to user input
- [ ] Large lists of accounts don't freeze the UI
- [ ] Memory usage is reasonable
- [ ] CPU usage is reasonable during operations

### Error Handling
- [ ] Network errors are handled gracefully
- [ ] Invalid API credentials show proper error
- [ ] Session errors are handled properly
- [ ] Flood wait errors are handled
- [ ] Application doesn't crash on errors

### Security
- [ ] API credentials are not logged
- [ ] Session files are properly secured
- [ ] No sensitive data in output files
- [ ] Proxy settings work (if configured)

## 📝 **Test Results Template**

```
Test Date: _______________
Tester: _________________

### Configuration
- API Credentials: [ ] Working [ ] Issues
- Session Management: [ ] Working [ ] Issues
- Settings: [ ] Working [ ] Issues

### Modules
- Checker: [ ] Working [ ] Issues
- Forwarder: [ ] Working [ ] Issues  
- Leaver: [ ] Working [ ] Issues

### Issues Found:
1. _________________
2. _________________
3. _________________

### Performance:
- Response Time: [ ] Good [ ] Slow [ ] Very Slow
- Memory Usage: [ ] Good [ ] High [ ] Very High
- Stability: [ ] Stable [ ] Some Crashes [ ] Unstable

### Overall Rating: [ ] Excellent [ ] Good [ ] Fair [ ] Poor

Notes:
_________________
_________________
_________________
```

## 🚀 **Quick Test Commands**

```bash
# Run basic functionality test
python manual_test.py

# Run quick import test
python quick_test.py

# Run comprehensive test
python test_installation.py

# Start GUI for manual testing
python main.py
```

## ⚠️ **Important Notes**

1. **Always test with test accounts first**
2. **Be careful with "Leave All" functionality**
3. **Monitor rate limits and delays**
4. **Keep backup of important sessions**
5. **Test in a controlled environment**

## 🆘 **Troubleshooting**

If you encounter issues:
1. Check the logs in logs/ directory
2. Verify API credentials
3. Ensure sessions are valid
4. Check network connectivity
5. Review TROUBLESHOOTING.md
