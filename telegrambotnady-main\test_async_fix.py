#!/usr/bin/env python3
"""
Test Async Fix Script
Run this to test if the async event loop issues are fixed
"""

import asyncio
import os
from telegram_automation_tool.config import load_config
from telegram_automation_tool.session_manager import SessionManager

async def test_async_fix():
    """Test the async fix"""
    print("🧪 Testing Async Fix")
    print("=" * 40)
    
    try:
        # Load config
        config = load_config()
        print(f"✅ Config loaded - API ID: {config.get('api_id', 'Not set')}")
        
        # Create session manager
        session_manager = SessionManager(config)
        
        # List existing sessions
        sessions = session_manager.get_session_files()
        print(f"\n📋 Found {len(sessions)} existing sessions:")
        for session in sessions:
            print(f"  - {session}")
        
        if not sessions:
            print("\n⚠️  No sessions found. Please create a session first using:")
            print("   1. GUI: python main.py -> Sessions tab -> Add Session")
            print("   2. Command line: python test_session_creation.py")
            return
        
        # Test session validation with proper error handling
        print(f"\n🔍 Testing session validation with async fix...")
        for session in sessions[:1]:  # Test first session
            print(f"Validating session: {session}")
            try:
                info = await session_manager.validate_session(session)
                print(f"  Status: {info['status']}")
                if info['status'] == 'valid':
                    user = info.get('user', {})
                    print(f"  User: {user.get('first_name', 'N/A')} {user.get('last_name', 'N/A')}")
                    print(f"  Username: @{user.get('username', 'N/A')}")
                    print(f"  Phone: {user.get('phone', 'N/A')}")
                elif info['status'] == 'not_authorized':
                    print(f"  Session not authorized: {info.get('error', 'Unknown error')}")
                elif info['status'] == 'error':
                    print(f"  Error: {info.get('error', 'Unknown error')}")
                else:
                    print(f"  Status: {info['status']} - {info.get('error', 'No error message')}")
            except Exception as e:
                print(f"  ❌ Error validating session: {e}")
                import traceback
                traceback.print_exc()
        
        print(f"\n🎉 Async fix test completed!")
        print("\nIf no errors occurred, the async fix is working!")
        print("\nNext steps:")
        print("1. Run the GUI: python main.py")
        print("2. Go to Sessions tab and click on sessions")
        print("3. The session details should load without errors")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_async_fix())
