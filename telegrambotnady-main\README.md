# Telegram Automation Tool v2.0

A comprehensive Telegram automation tool built with Python, Telethon, and Tkinter. This tool provides powerful features for account checking, message forwarding, group management, and session management with proxy support.

## 🚀 Features

### 🔍 Checker Module
- **Account Validation**: Check if usernames, phone numbers, or session accounts are valid
- **Comprehensive Info**: Get detailed account information including:
  - Username, name, bio
  - Premium status ✅/❌
  - Verified badge ✅/❌
  - Profile picture presence
  - Last seen status
  - Common group count
- **Multiple Formats**: Support for JSON and CSV output
- **Rate Limiting**: Built-in delays to prevent spam detection

### 📤 Forwarder Module
- **Real-time Forwarding**: Monitor source groups/channels and forward messages instantly
- **Session Rotation**: Use multiple sender accounts to distribute load
- **Message Types**: Support for text, images, videos, files, stickers
- **Sender Tag Removal**: Option to re-upload media without original sender info
- **Smart Error Handling**: Automatic handling of blocks, rate limits, and errors
- **Configurable Delays**: Random delay ranges between forwards

### 🚪 Leaver Module
- **Multiple Modes**:
  - **Specific**: Leave specific groups/channels by ID or username
  - **All**: Leave all joined groups and channels
  - **Inactive**: Leave groups inactive for specified number of days
- **Smart Detection**: Automatically detect group types (supergroups, channels, regular groups)
- **Progress Tracking**: Real-time progress and detailed logging
- **Error Recovery**: Handle various error conditions gracefully

### 🔑 Session Management
- **Easy Creation**: Add new sessions with phone verification
- **Status Monitoring**: Check session validity and account information
- **Bulk Operations**: Rename, delete, and manage multiple sessions
- **Proxy Support**: Assign different proxies to different sessions
- **Session Validation**: Verify session status (valid, expired, 2FA required, etc.)

### ⚙️ Advanced Features
- **Proxy Support**: HTTP/SOCKS5 proxy support with rotation
- **Anti-Ban Logic**: Built-in rate limiting and delay mechanisms
- **Comprehensive Logging**: Detailed logs for all operations
- **Configuration Management**: Save/load configuration files
- **Dark Mode**: Toggle between light and dark themes
- **Export Results**: JSON and CSV output formats

## 📋 Requirements

- Python 3.7+
- Telegram API credentials (API ID and API Hash)
- Required Python packages (see requirements.txt)

## 🛠 Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd andytelbot
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure API credentials**:
   - Get your API ID and API Hash from [my.telegram.org](https://my.telegram.org)
   - Update `config.json` with your credentials

4. **Run the application**:
   ```bash
   python main.py
   ```

## 📁 Project Structure

```
andytelbot/
├── main.py                          # Main entry point
├── requirements.txt                 # Python dependencies
├── config.json                     # Configuration file
├── telegram_automation_tool/       # Main package
│   ├── __init__.py
│   ├── app.py                      # Main GUI application
│   ├── config.py                   # Configuration management
│   ├── logger.py                   # Logging setup
│   ├── session_manager.py          # Session management
│   ├── checker.py                  # Account checking module
│   ├── forwarder.py                # Message forwarding module
│   ├── leaver.py                   # Group leaving module
│   └── utils.py                    # Utility functions
├── sessions/                       # Telegram session files
├── logs/                          # Application logs
├── output/                        # Results and reports
│   ├── checker/                   # Checker results
│   ├── forwarder/                 # Forwarder reports
│   └── leaver/                    # Leaver reports
└── temp/                          # Temporary files
```

## ⚙️ Configuration

### Basic Configuration (config.json)
```json
{
  "api_id": ********,
  "api_hash": "your_api_hash_here",
  "sessions_dir": "sessions",
  "log_dir": "logs",
  "output_dir": "output",
  "proxy": {
    "enabled": false,
    "type": "socks5",
    "host": "127.0.0.1",
    "port": 9050,
    "username": "",
    "password": ""
  }
}
```

### Module-Specific Settings
- **Checker**: Delay between checks, output formats
- **Forwarder**: Delay ranges, session rotation settings
- **Leaver**: Delay between leaves, inactivity thresholds

## 🚀 Usage Guide

### 1. Session Management
1. Go to the **Sessions** tab
2. Click **Add Session** and enter your phone number
3. Enter the verification code sent to your phone
4. If 2FA is enabled, enter your password
5. The session will be saved and ready to use

### 2. Account Checking
1. Go to the **Checker** tab
2. Enter usernames or phone numbers (comma separated)
3. Select a session to use for checking
4. Click **Start Checking**
5. View results in the log and check output files

### 3. Message Forwarding
1. Go to the **Forwarder** tab
2. Select a listener session (monitors source)
3. Enter sender sessions (comma separated)
4. Specify source group/channel
5. Enter target groups/channels
6. Configure options and click **Start Forwarding**

### 4. Group Management
1. Go to the **Leaver** tab
2. Select a session
3. Choose mode (specific, all, or inactive)
4. Configure targets or thresholds
5. Click **Start Leaving**

## 🔧 Advanced Features

### Proxy Configuration
```json
{
  "proxy": {
    "enabled": true,
    "type": "socks5",
    "host": "proxy.example.com",
    "port": 1080,
    "username": "user",
    "password": "pass"
  }
}
```

### Multiple Proxies
```json
{
  "proxy": {
    "enabled": true,
    "proxies": [
      {
        "type": "socks5",
        "host": "proxy1.example.com",
        "port": 1080
      },
      {
        "type": "http",
        "host": "proxy2.example.com",
        "port": 8080
      }
    ]
  }
}
```

## 📊 Output Formats

### Checker Results
- **JSON**: Detailed account information
- **CSV**: Tabular format for analysis
- **Logs**: Real-time operation logs

### Forwarder Reports
- **Statistics**: Forwarded count, errors, timing
- **Details**: Per-message forwarding status
- **Session Performance**: Per-session statistics

### Leaver Reports
- **Summary**: Total left, errors, skipped
- **Details**: Per-group leaving status
- **Timing**: Start/end times and duration

## 🛡️ Safety Features

- **Rate Limiting**: Automatic delays to prevent spam detection
- **Error Handling**: Graceful handling of blocks and errors
- **Session Rotation**: Distribute load across multiple accounts
- **Proxy Support**: Use proxies to avoid IP-based restrictions
- **Logging**: Comprehensive logging for debugging and monitoring

## ⚠️ Important Notes

1. **API Limits**: Respect Telegram's API limits and terms of service
2. **Account Safety**: Use responsibly to avoid account restrictions
3. **Proxy Usage**: Ensure proxies are reliable and secure
4. **Session Security**: Keep session files secure and private
5. **Testing**: Test with small batches before large operations

## 🐛 Troubleshooting

### Common Issues

1. **Session Not Authorized**
   - Delete session file and recreate
   - Check phone number format
   - Verify 2FA password

2. **Flood Wait Errors**
   - Increase delays between operations
   - Use fewer sessions simultaneously
   - Wait for flood wait to expire

3. **Proxy Connection Issues**
   - Verify proxy credentials
   - Check proxy server status
   - Try different proxy types

4. **API Errors**
   - Verify API ID and Hash
   - Check internet connection
   - Ensure Telegram service is available

## 📝 License

This project is for educational purposes. Use responsibly and in compliance with Telegram's terms of service.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs for error details
3. Create an issue with detailed information

---

**Disclaimer**: This tool is for educational and legitimate automation purposes only. Users are responsible for complying with Telegram's terms of service and applicable laws.
