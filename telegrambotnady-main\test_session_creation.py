#!/usr/bin/env python3
"""
Test Session Creation Script
Run this to test session creation without the GUI
"""

import asyncio
import os
from telegram_automation_tool.config import load_config
from telegram_automation_tool.session_manager import SessionManager

async def test_session_creation():
    """Test session creation process"""
    print("🧪 Testing Session Creation")
    print("=" * 40)
    
    # Load configuration
    try:
        config = load_config()
        print(f"✅ Config loaded - API ID: {config.get('api_id', 'Not set')}")
    except Exception as e:
        print(f"❌ Config error: {e}")
        return
    
    # Create session manager
    session_manager = SessionManager(config)
    
    # Test phone number (replace with your actual phone number)
    test_phone = input("Enter phone number (with country code, e.g., +1234567890): ").strip()
    
    if not test_phone:
        print("❌ No phone number provided")
        return
    
    print(f"\n📱 Creating session for: {test_phone}")
    
    try:
        # Step 1: Create new session
        result = await session_manager.create_new_session(test_phone)
        print(f"Step 1 Result: {result['status']}")
        
        if result['status'] == 'code_sent':
            session_name = result['session_name']
            phone_code_hash = result.get('phone_code_hash')
            print(f"✅ Code sent to {test_phone}")
            print(f"📁 Session name: {session_name}")
            
            # Step 2: Get verification code
            code = input("Enter the verification code sent to your phone: ").strip()
            
            if code:
                # Step 3: Verify code
                result = await session_manager.verify_code(session_name, test_phone, code, phone_code_hash)
                print(f"Step 3 Result: {result['status']}")
                
                if result['status'] == 'success':
                    print("🎉 Session created successfully!")
                    print(f"User: {result['user']['first_name']} {result['user']['last_name']}")
                    print(f"Username: @{result['user']['username']}")
                    print(f"User ID: {result['user']['id']}")
                    
                    # Verify session works
                    validation = await session_manager.validate_session(session_name)
                    print(f"Session validation: {validation['status']}")
                    
                elif result['status'] == '2fa_required':
                    print("🔐 2FA required")
                    password = input("Enter your 2FA password: ").strip()
                    
                    if password:
                        result = await session_manager.verify_code(session_name, test_phone, code, phone_code_hash, password)
                        if result['status'] == 'success':
                            print("🎉 Session created successfully with 2FA!")
                            print(f"User: {result['user']['first_name']} {result['user']['last_name']}")
                        else:
                            print(f"❌ 2FA verification failed: {result.get('error')}")
                    else:
                        print("❌ 2FA password not provided")
                        
                else:
                    print(f"❌ Code verification failed: {result.get('error')}")
            else:
                print("❌ No verification code provided")
                
        elif result['status'] == 'authorized':
            print("✅ Session already authorized!")
            print(f"User: {result['user']['first_name']} {result['user']['last_name']}")
            
        else:
            print(f"❌ Session creation failed: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ Error during session creation: {e}")
    
    # List all sessions
    print(f"\n📋 Available sessions:")
    sessions = session_manager.get_session_files()
    for session in sessions:
        print(f"  - {session}")

if __name__ == "__main__":
    asyncio.run(test_session_creation())
