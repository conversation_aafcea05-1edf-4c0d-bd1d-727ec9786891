import os
import json
import asyncio
import csv
from datetime import datetime
from telethon.errors import (
    SessionPasswordNeededError,
    FloodWaitError,
    UsernameNotOccupiedError,
    UsernameInvalidError,
    PhoneNumberInvalidError,
    PhoneCodeInvalidError,
)
from telethon.tl.functions.users import GetFullUserRequest
from telethon.tl.functions.messages import GetCommonChatsRequest
from telethon.tl.types import (
    UserStatusRecently,
    UserStatusLastWeek,
    UserStatusLastMonth,
    UserStatusOnline,
    UserStatusOffline,
)
from telegram_automation_tool.session_manager import SessionManager
from telegram_automation_tool.logger import setup_logger


class Checker:
    def __init__(self, config, logger=None):
        self.config = config
        self.logger = logger or setup_logger('checker', 'logs/checker.log')
        self.session_manager = SessionManager(config)
        
        # Create output directories
        self.output_dir = self.config.get("checker", {}).get("output_folder", "output/checker")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Cancellation flag
        self._cancel_event = asyncio.Event()
        
        # Rate limiting
        self.delay_between_checks = self.config.get("checker", {}).get("delay_between_checks", 1.0)

    async def _has_profile_photo(self, client, entity):
        """Check if user has profile photo"""
        try:
            photos = await client.get_profile_photos(entity, limit=1)
            return len(photos) > 0
        except Exception as e:
            self.logger.debug(f"Error checking profile photo for {entity}: {e}")
            return False

    async def _get_common_groups(self, client, user_id):
        """Get count of common groups with the user"""
        try:
            common_chats = await client(GetCommonChatsRequest(
                user_id=user_id,
                max_id=0,
                limit=100
            ))
            return len(common_chats.chats)
        except Exception as e:
            self.logger.debug(f"Error getting common groups for {user_id}: {e}")
            return 0

    def _last_seen_str(self, status_obj):
        """Convert status object to readable string"""
        if status_obj is None:
            return "Unknown"
        try:
            if isinstance(status_obj, UserStatusOnline):
                return "Online"
            elif isinstance(status_obj, UserStatusRecently):
                return "Recently"
            elif isinstance(status_obj, UserStatusLastWeek):
                return "Within last week"
            elif isinstance(status_obj, UserStatusLastMonth):
                return "Within last month"
            elif isinstance(status_obj, UserStatusOffline):
                if hasattr(status_obj, "was_online"):
                    return f"Last seen: {status_obj.was_online}"
                return "Offline"
            else:
                return str(status_obj)
        except Exception:
            return "Unknown"

    async def check_account(self, client, identifier):
        """Check a single account and return detailed information"""
        if self._cancel_event.is_set():
            self.logger.info(f"Checker cancelled before checking {identifier}")
            return "Cancelled", None
            
        try:
            # Add delay to prevent rate limiting
            await asyncio.sleep(self.delay_between_checks)
            
            try:
                entity = await client.get_entity(identifier)
            except ValueError as e:
                self.logger.warning(f"❌ Could not find entity for {identifier}: {e}")
                return "Not Found", None
            except Exception as e:
                self.logger.error(f"❌ Error resolving entity for {identifier}: {e}")
                return "Error", None
            
            # Get full user info
            try:
                full = await client(GetFullUserRequest(entity.id))
            except Exception as e:
                self.logger.debug(f"Could not get full user info for {identifier}: {e}")
                full = entity

            user_obj = getattr(full, "user", None) or full or entity

            # Extract user information
            user_id = getattr(user_obj, "id", None) or getattr(entity, "id", None)
            username = getattr(user_obj, "username", None) or getattr(entity, "username", None)
            first_name = getattr(user_obj, "first_name", None) or getattr(entity, "first_name", None)
            last_name = getattr(user_obj, "last_name", None) or getattr(entity, "last_name", None)
            phone = getattr(user_obj, "phone", None) or getattr(entity, "phone", None)
            
            # Get additional info
            bio = getattr(full, "about", None) or getattr(user_obj, "about", None)
            is_premium = bool(getattr(user_obj, "premium", False) or getattr(entity, "premium", False))
            is_verified = bool(getattr(user_obj, "verified", False) or getattr(entity, "verified", False))
            is_bot = bool(getattr(user_obj, "bot", False) or getattr(entity, "bot", False))
            is_scam = bool(getattr(user_obj, "scam", False) or getattr(entity, "scam", False))
            is_fake = bool(getattr(user_obj, "fake", False) or getattr(entity, "fake", False))

            status_obj = getattr(user_obj, "status", None) or getattr(full, "status", None) or getattr(entity, "status", None)
            last_seen = self._last_seen_str(status_obj)

            # Check profile photo
            profile_photo = await self._has_profile_photo(client, entity)
            
            # Get common groups count
            common_groups = await self._get_common_groups(client, user_id)

            info = {
                "id": user_id,
                "username": username,
                "first_name": first_name,
                "last_name": last_name,
                "phone": phone,
                "bio": bio,
                "is_premium": is_premium,
                "is_verified": is_verified,
                "is_bot": is_bot,
                "is_scam": is_scam,
                "is_fake": is_fake,
                "profile_photo": profile_photo,
                "last_seen": last_seen,
                "common_groups": common_groups,
                "checked_at": datetime.now().isoformat()
            }

            self.logger.info(f"✅ Checked user {identifier}: {username or first_name} (ID: {user_id})")
            return "Valid", info

        except SessionPasswordNeededError:
            self.logger.error(f"🔒 Account {identifier} has 2FA enabled.")
            return "2FA", None
        except UsernameNotOccupiedError:
            self.logger.warning(f"❌ Username {identifier} not occupied.")
            return "Not Registered", None
        except UsernameInvalidError:
            self.logger.warning(f"❌ Invalid username {identifier}.")
            return "Invalid", None
        except PhoneNumberInvalidError:
            self.logger.warning(f"❌ Invalid phone number {identifier}.")
            return "Invalid Phone", None
        except PhoneCodeInvalidError:
            self.logger.error(f"❌ Invalid phone code for {identifier}.")
            return "Invalid Code", None
        except FloodWaitError as e:
            self.logger.error(f"⏳ Flood wait for {e.seconds} seconds on {identifier}")
            return "FloodWait", None
        except Exception as e:
            self.logger.exception(f"❌ Error checking {identifier}: {e}")
            return "Error", None

    async def run(self, identifiers, session_name, progress_callback=None):
        """Run the checker with multiple identifiers"""
        client = self.session_manager.create_client(session_name)
        await client.start()
        results = {}

        total = len(identifiers)
        for idx, identifier in enumerate(identifiers, start=1):
            if self._cancel_event.is_set():
                self.logger.info("Checker cancelled by user.")
                break
                
            self.logger.info(f"Checking {identifier} ({idx}/{total})...")
            
            if progress_callback:
                try:
                    await progress_callback(idx, total, identifier)
                except Exception as e:
                    self.logger.error(f"Error in progress callback: {e}")
                    
            status, info = await self.check_account(client, identifier)
            results[identifier] = {"status": status, "info": info}

        await client.disconnect()

        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_name = session_name.replace(" ", "_")
        
        # Save as JSON
        json_path = os.path.join(self.output_dir, f"{safe_name}_checker_results_{timestamp}.json")
        with open(json_path, "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=4)
            
        # Save as CSV
        csv_path = os.path.join(self.output_dir, f"{safe_name}_checker_results_{timestamp}.csv")
        self._save_to_csv(results, csv_path)
        
        self.logger.info(f"Results saved to {json_path} and {csv_path}")
        return results

    def _save_to_csv(self, results, csv_path):
        """Save results to CSV format"""
        try:
            with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'identifier', 'status', 'id', 'username', 'first_name', 'last_name',
                    'phone', 'bio', 'is_premium', 'is_verified', 'is_bot', 'is_scam',
                    'is_fake', 'profile_photo', 'last_seen', 'common_groups', 'checked_at'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for identifier, result in results.items():
                    row = {'identifier': identifier, 'status': result['status']}
                    if result['info']:
                        row.update(result['info'])
                    writer.writerow(row)
        except Exception as e:
            self.logger.error(f"Error saving CSV: {e}")

    def cancel(self):
        """External call to request cancelling the run."""
        self._cancel_event.set()
