import os
import json
import yaml  # pip install pyyaml
from jsonschema import validate as jsonschema_validate, ValidationError  # pip install jsonschema

CONFIG_SCHEMA = {
    "type": "object",
    "properties": {
        "api_id": {"type": "integer"},
        "api_hash": {"type": "string"},
        "sessions_dir": {"type": "string"},
        "log_dir": {"type": "string"},
        "temp_dir": {"type": "string"},
        "output_dir": {"type": "string"},
        "default_delay_min": {"type": "number"},
        "default_delay_max": {"type": "number"},
        "proxy": {
            "type": "object",
            "properties": {
                "enabled": {"type": "boolean"},
                "type": {"type": "string"},
                "host": {"type": "string"},
                "port": {"type": "integer"},
                "username": {"type": "string"},
                "password": {"type": "string"},
            },
            "required": ["enabled", "type", "host", "port", "username", "password"],
            "additionalProperties": False,
        },
        "checker": {
            "type": "object",
            "properties": {
                "input_file": {"type": "string"},
                "output_folder": {"type": "string"},
            },
            "required": ["input_file", "output_folder"],
            "additionalProperties": False,
        },
        "forwarder": {
            "type": "object",
            "properties": {
                "delay_min": {"type": "number"},
                "delay_max": {"type": "number"},
                "sessions_folder": {"type": "string"},
            },
            "required": ["delay_min", "delay_max", "sessions_folder"],
            "additionalProperties": False,
        },
        "leaver": {
            "type": "object",
            "properties": {
                "sessions_folder": {"type": "string"},
                "delay": {"type": "number"},
            },
            "required": ["sessions_folder", "delay"],
            "additionalProperties": False,
        },
    },
    "required": ["api_id", "api_hash"],
    "additionalProperties": False,
}

DEFAULT_CONFIG = {
    "api_id": ********,  # Replace with your API ID
    "api_hash": "5af1121396896b2c3bc0973479ea86d2",  # Replace with your API Hash
    "sessions_dir": "sessions",
    "log_dir": "logs",
    "temp_dir": "temp",
    "output_dir": "output",
    "default_delay_min": 2.0,
    "default_delay_max": 5.0,
    "proxy": {
        "enabled": False,
        "type": "socks5",
        "host": "127.0.0.1",
        "port": 9050,
        "username": "",
        "password": "",
    },
    "checker": {
        "input_file": "input_accounts.txt",
        "output_folder": "output/checker",
    },
    "forwarder": {
        "delay_min": 2.0,
        "delay_max": 5.0,
        "sessions_folder": "sessions",
    },
    "leaver": {
        "sessions_folder": "sessions",
        "delay": 3,
    },
}

CONFIG_PATH_JSON = "config.json"
CONFIG_PATH_YAML = "config.yaml"


def load_config(path=None):
    """Load config from JSON or YAML file. Use default if not found."""
    if path is None:
        if os.path.exists(CONFIG_PATH_JSON):
            path = CONFIG_PATH_JSON
        elif os.path.exists(CONFIG_PATH_YAML):
            path = CONFIG_PATH_YAML
        else:
            return DEFAULT_CONFIG.copy()

    try:
        with open(path, "r", encoding="utf-8") as f:
            if path.endswith(".json"):
                config = json.load(f)
            else:
                config = yaml.safe_load(f)
        validate_config(config)
        return config
    except Exception as e:
        print(f"Error loading config from {path}: {e}")
        return DEFAULT_CONFIG.copy()


def save_config(config, path=None):
    """Save config to JSON file by default."""
    if path is None:
        path = CONFIG_PATH_JSON

    try:
        if path.endswith(".json"):
            with open(path, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2)
        else:
            with open(path, "w", encoding="utf-8") as f:
                yaml.safe_dump(config, f)
        return True
    except Exception as e:
        print(f"Error saving config to {path}: {e}")
        return False


def validate_config(config):
    """Validate config dictionary against schema."""
    jsonschema_validate(instance=config, schema=CONFIG_SCHEMA)
