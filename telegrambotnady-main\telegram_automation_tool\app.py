import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, simpledialog, filedialog
import threading
import asyncio
import datetime
import os
import shutil
from telegram_automation_tool.config import load_config, save_config
from telegram_automation_tool.logger import setup_logger
from telegram_automation_tool.checker import Checker
from telegram_automation_tool.forwarder import Forwarder
from telegram_automation_tool.leaver import Leaver
from telegram_automation_tool.session_manager import SessionManager

# Async helper
def run_asyncio_task(coro):
    """Run async task in a thread-safe way"""
    try:
        # Create a new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro)
        finally:
            loop.close()
    except Exception as e:
        print(f"Error in async task: {e}")
        return None

class TelegramAutomationApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("Telegram Automation Tool v2.0")
        self.geometry("1000x800")
        self.minsize(900, 700)
        self.style = ttk.Style(self)

        # Set default font and styling
        default_font = ("Segoe UI", 10)
        self.option_add("*Font", default_font)
        self.style.theme_use('clam')

        # Load configuration
        self.config = load_config()
        self.logger = setup_logger('app', os.path.join(self.config.get("log_dir", "logs"), 'app.log'))
        self.session_manager = SessionManager(self.config)

        # Initialize instances
        self.forwarder_instance = None
        self.checker_instance = None
        self.leaver_instance = None
        
        # Cancel events
        self.forwarder_cancel_event = threading.Event()
        self.checker_cancel_event = threading.Event()
        self.leaver_cancel_event = threading.Event()

        # Create directories
        self.sessions_dir = self.config.get("sessions_dir", "sessions")
        os.makedirs(self.sessions_dir, exist_ok=True)
        os.makedirs(self.config.get("log_dir", "logs"), exist_ok=True)
        
        # Create output directories for each module
        output_dir = self.config.get("output_dir", "output")
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, "checker"), exist_ok=True)
        os.makedirs(os.path.join(output_dir, "forwarder"), exist_ok=True)
        os.makedirs(os.path.join(output_dir, "leaver"), exist_ok=True)

        self.create_menu()
        self.create_widgets()
        self.protocol("WM_DELETE_WINDOW", self.on_close)

        # Dark mode state
        self.dark_mode = False
        
        # Load sessions on startup
        self.reload_sessions()

    def create_menu(self):
        menubar = tk.Menu(self)
        self.configure(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="Load Config", command=self.load_config_file)
        file_menu.add_command(label="Save Config", command=self.save_config_file)
        file_menu.add_separator()
        file_menu.add_command(label="Reload Sessions", command=self.reload_sessions)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_close)
        menubar.add_cascade(label="File", menu=file_menu)

        # View menu
        view_menu = tk.Menu(menubar, tearoff=0)
        view_menu.add_command(label="Toggle Dark Mode", command=self.toggle_dark_mode)
        menubar.add_cascade(label="View", menu=view_menu)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label="About", command=self.show_about)
        help_menu.add_command(label="Documentation", command=self.show_documentation)
        menubar.add_cascade(label="Help", menu=help_menu)

    def create_widgets(self):
        # Main notebook
        self.notebook = ttk.Notebook(self)
        self.notebook.pack(expand=True, fill='both', padx=5, pady=5)

        # Create tabs
        self.checker_tab = ttk.Frame(self.notebook)
        self.forwarder_tab = ttk.Frame(self.notebook)
        self.leaver_tab = ttk.Frame(self.notebook)
        self.sessions_tab = ttk.Frame(self.notebook)
        self.settings_tab = ttk.Frame(self.notebook)

        self.notebook.add(self.checker_tab, text="🔍 Checker")
        self.notebook.add(self.forwarder_tab, text="📤 Forwarder")
        self.notebook.add(self.leaver_tab, text="🚪 Leaver")
        self.notebook.add(self.sessions_tab, text="🔑 Sessions")
        self.notebook.add(self.settings_tab, text="⚙️ Settings")

        # Build each tab
        self._build_checker_tab()
        self._build_forwarder_tab()
        self._build_leaver_tab()
        self._build_sessions_tab()
        self._build_settings_tab()

        # Status bar
        self.status_bar = ttk.Label(self, text="Ready", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def _build_checker_tab(self):
        frame = self.checker_tab

        # Input section
        input_frame = ttk.LabelFrame(frame, text="Input Configuration", padding=10)
        input_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(input_frame, text="Identifiers (usernames/phone numbers, comma separated):").pack(anchor='w')
        self.checker_identifiers_entry = ttk.Entry(input_frame, width=80)
        self.checker_identifiers_entry.pack(fill='x', pady=2)

        ttk.Label(input_frame, text="Session name:").pack(anchor='w', pady=(10, 0))
        self.checker_session_combobox = ttk.Combobox(input_frame, width=50, state="readonly")
        self.checker_session_combobox.pack(anchor='w', pady=2)

        # Control buttons
        btn_frame = ttk.Frame(frame)
        btn_frame.pack(pady=10, padx=10, fill='x')

        self.checker_start_btn = ttk.Button(btn_frame, text="Start Checking", command=self.start_checker)
        self.checker_start_btn.pack(side='left', padx=(0, 5))

        self.checker_stop_btn = ttk.Button(btn_frame, text="Stop", command=self.stop_checker, state='disabled')
        self.checker_stop_btn.pack(side='left', padx=(0, 5))

        ttk.Button(btn_frame, text="Clear Log", command=self.clear_checker_log).pack(side='left', padx=(0, 5))
        ttk.Button(btn_frame, text="Open Results", command=self.open_checker_results).pack(side='left')

        # Progress bar
        self.checker_progress = ttk.Progressbar(frame, mode='determinate')
        self.checker_progress.pack(fill='x', padx=10, pady=5)

        # Log area
        log_frame = ttk.LabelFrame(frame, text="Log", padding=10)
        log_frame.pack(expand=True, fill='both', padx=10, pady=5)

        self.checker_log = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.checker_log.pack(expand=True, fill='both')

    def _build_forwarder_tab(self):
        frame = self.forwarder_tab

        # Configuration section
        config_frame = ttk.LabelFrame(frame, text="Configuration", padding=10)
        config_frame.pack(fill='x', padx=10, pady=5)

        # Listener session
        ttk.Label(config_frame, text="Listener Session:").pack(anchor='w')
        self.forwarder_listener_combobox = ttk.Combobox(config_frame, width=50, state="readonly")
        self.forwarder_listener_combobox.pack(anchor='w', pady=2)

        # Source
        ttk.Label(config_frame, text="Source (group/channel ID or username):").pack(anchor='w', pady=(10, 0))
        self.forwarder_source_entry = ttk.Entry(config_frame, width=50)
        self.forwarder_source_entry.pack(anchor='w', pady=2)

        # Sender sessions
        ttk.Label(config_frame, text="Sender Sessions (comma separated):").pack(anchor='w', pady=(10, 0))
        self.forwarder_senders_entry = ttk.Entry(config_frame, width=50)
        self.forwarder_senders_entry.pack(anchor='w', pady=2)

        # Targets
        ttk.Label(config_frame, text="Targets (comma separated):").pack(anchor='w', pady=(10, 0))
        self.forwarder_targets_entry = ttk.Entry(config_frame, width=50)
        self.forwarder_targets_entry.pack(anchor='w', pady=2)

        # Control buttons
        btn_frame = ttk.Frame(frame)
        btn_frame.pack(pady=10, padx=10, fill='x')

        self.forwarder_start_btn = ttk.Button(btn_frame, text="Start Forwarding", command=self.start_forwarder)
        self.forwarder_start_btn.pack(side='left', padx=(0, 5))

        self.forwarder_stop_btn = ttk.Button(btn_frame, text="Stop", command=self.stop_forwarder, state='disabled')
        self.forwarder_stop_btn.pack(side='left', padx=(0, 5))

        ttk.Button(btn_frame, text="Clear Log", command=self.clear_forwarder_log).pack(side='left', padx=(0, 5))
        ttk.Button(btn_frame, text="Open Results", command=self.open_forwarder_results).pack(side='left')

        # Log area
        log_frame = ttk.LabelFrame(frame, text="Log", padding=10)
        log_frame.pack(expand=True, fill='both', padx=10, pady=5)

        self.forwarder_log = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.forwarder_log.pack(expand=True, fill='both')

    def _build_leaver_tab(self):
        frame = self.leaver_tab

        # Configuration section
        config_frame = ttk.LabelFrame(frame, text="Configuration", padding=10)
        config_frame.pack(fill='x', padx=10, pady=5)

        # Session selection
        ttk.Label(config_frame, text="Session:").pack(anchor='w')
        self.leaver_session_combobox = ttk.Combobox(config_frame, width=50, state="readonly")
        self.leaver_session_combobox.pack(anchor='w', pady=2)

        # Mode selection
        ttk.Label(config_frame, text="Mode:").pack(anchor='w', pady=(10, 0))
        self.leaver_mode_var = tk.StringVar(value="specific")
        mode_frame = ttk.Frame(config_frame)
        mode_frame.pack(fill='x', pady=2)
        ttk.Radiobutton(mode_frame, text="Specific targets", variable=self.leaver_mode_var, value="specific", command=self._update_leaver_mode).pack(anchor='w')
        ttk.Radiobutton(mode_frame, text="All groups/channels", variable=self.leaver_mode_var, value="all", command=self._update_leaver_mode).pack(anchor='w')
        ttk.Radiobutton(mode_frame, text="Inactive groups only", variable=self.leaver_mode_var, value="inactive", command=self._update_leaver_mode).pack(anchor='w')
        
        # Specific targets input
        self.leaver_targets_frame = ttk.Frame(config_frame)
        self.leaver_targets_frame.pack(fill='x', pady=5)
        ttk.Label(self.leaver_targets_frame, text="Targets (comma separated):").pack(anchor='w')
        self.leaver_targets_entry = ttk.Entry(self.leaver_targets_frame, width=50)
        self.leaver_targets_entry.pack(anchor='w', pady=2)
        
        # Inactive threshold
        self.leaver_inactive_frame = ttk.Frame(config_frame)
        ttk.Label(self.leaver_inactive_frame, text="Days inactive threshold:").pack(anchor='w')
        self.leaver_days_var = tk.StringVar(value="30")
        ttk.Entry(self.leaver_inactive_frame, textvariable=self.leaver_days_var, width=10).pack(anchor='w', pady=2)
        
        # Initialize UI based on default mode
        self._update_leaver_mode()

        # Control buttons
        btn_frame = ttk.Frame(frame)
        btn_frame.pack(pady=10, padx=10, fill='x')

        self.leaver_start_btn = ttk.Button(btn_frame, text="Start Leaving", command=self.start_leaver)
        self.leaver_start_btn.pack(side='left', padx=(0, 5))

        self.leaver_stop_btn = ttk.Button(btn_frame, text="Stop", command=self.stop_leaver, state='disabled')
        self.leaver_stop_btn.pack(side='left', padx=(0, 5))

        ttk.Button(btn_frame, text="Clear Log", command=self.clear_leaver_log).pack(side='left', padx=(0, 5))
        ttk.Button(btn_frame, text="Open Results", command=self.open_leaver_results).pack(side='left')

        # Log area
        log_frame = ttk.LabelFrame(frame, text="Log", padding=10)
        log_frame.pack(expand=True, fill='both', padx=10, pady=5)

        self.leaver_log = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.leaver_log.pack(expand=True, fill='both')

    def _build_sessions_tab(self):
        frame = self.sessions_tab

        # Create a split view: sessions list on left, details on right
        paned_window = ttk.PanedWindow(frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill='both', expand=True, padx=10, pady=5)

        # Left side: Session list
        left_frame = ttk.Frame(paned_window)
        paned_window.add(left_frame, weight=1)

        # Session list
        list_frame = ttk.LabelFrame(left_frame, text="Available Sessions", padding=10)
        list_frame.pack(fill='both', expand=True)

        # Session listbox with status indicators
        self.sessions_listbox = tk.Listbox(list_frame, height=15, selectmode=tk.SINGLE)
        self.sessions_listbox.pack(side='left', fill='both', expand=True)
        self.sessions_listbox.bind('<<ListboxSelect>>', self.on_session_selected)
        self.sessions_listbox.bind('<Double-Button-1>', self.on_session_double_click)

        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.sessions_listbox.yview)
        scrollbar.pack(side='right', fill='y')
        self.sessions_listbox.configure(yscrollcommand=scrollbar.set)

        # Session management buttons
        btn_frame = ttk.Frame(left_frame)
        btn_frame.pack(pady=10, fill='x')

        ttk.Button(btn_frame, text="Add Session", command=self.add_session).pack(side='left', padx=(0, 5))
        ttk.Button(btn_frame, text="Delete Session", command=self.delete_session).pack(side='left', padx=(0, 5))
        ttk.Button(btn_frame, text="Refresh", command=self.reload_sessions).pack(side='left')

        # Right side: Session details
        right_frame = ttk.Frame(paned_window)
        paned_window.add(right_frame, weight=2)

        # Session details
        details_frame = ttk.LabelFrame(right_frame, text="Session Details", padding=10)
        details_frame.pack(fill='both', expand=True)

        # Session info display
        self.session_info_text = scrolledtext.ScrolledText(details_frame, height=10, wrap=tk.WORD, state=tk.DISABLED)
        self.session_info_text.pack(fill='both', expand=True, pady=(0, 10))

        # Session action buttons
        action_frame = ttk.Frame(details_frame)
        action_frame.pack(fill='x')

        self.resend_code_btn = ttk.Button(action_frame, text="Resend Verification Code", command=self.resend_verification_code, state='disabled')
        self.resend_code_btn.pack(side='left', padx=(0, 5))

        self.rename_session_btn = ttk.Button(action_frame, text="Rename Session", command=self.rename_session, state='disabled')
        self.rename_session_btn.pack(side='left', padx=(0, 5))

        self.validate_session_btn = ttk.Button(action_frame, text="Validate Session", command=self.validate_selected_session, state='disabled')
        self.validate_session_btn.pack(side='left')

        # Store selected session
        self.selected_session = None

    def _build_settings_tab(self):
        frame = self.settings_tab

        # API Configuration
        api_frame = ttk.LabelFrame(frame, text="API Configuration", padding=10)
        api_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(api_frame, text="API ID:").pack(anchor='w')
        self.api_id_var = tk.StringVar(value=str(self.config.get("api_id", "")))
        ttk.Entry(api_frame, textvariable=self.api_id_var, width=50).pack(anchor='w', pady=2)

        ttk.Label(api_frame, text="API Hash:").pack(anchor='w', pady=(10, 0))
        self.api_hash_var = tk.StringVar(value=self.config.get("api_hash", ""))
        ttk.Entry(api_frame, textvariable=self.api_hash_var, width=50, show="*").pack(anchor='w', pady=2)

        # Save button
        ttk.Button(frame, text="Save Settings", command=self.save_settings).pack(pady=10)

    def log_checker(self, message, error=False):
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.checker_log.insert(tk.END, f"[{timestamp}] {message}\n")
        self.checker_log.see(tk.END)
        self.logger.info(message)

    def log_forwarder(self, message, error=False):
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.forwarder_log.insert(tk.END, f"[{timestamp}] {message}\n")
        self.forwarder_log.see(tk.END)
        self.logger.info(message)

    def log_leaver(self, message, error=False):
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.leaver_log.insert(tk.END, f"[{timestamp}] {message}\n")
        self.leaver_log.see(tk.END)
        self.logger.info(message)

    def set_checker_controls(self, enabled: bool):
        self.checker_start_btn.config(state='normal' if enabled else 'disabled')
        self.checker_stop_btn.config(state='disabled' if enabled else 'normal')

    def set_forwarder_controls(self, enabled: bool):
        self.forwarder_start_btn.config(state='normal' if enabled else 'disabled')
        self.forwarder_stop_btn.config(state='disabled' if enabled else 'normal')

    def set_leaver_controls(self, enabled: bool):
        self.leaver_start_btn.config(state='normal' if enabled else 'disabled')
        self.leaver_stop_btn.config(state='disabled' if enabled else 'normal')

    def start_checker(self):
        identifiers = self.checker_identifiers_entry.get().strip()
        session_name = self.checker_session_combobox.get()
        
        if not identifiers or not session_name:
            messagebox.showerror("Error", "Please enter identifiers and select a session")
            return

        identifiers = [x.strip() for x in identifiers.split(',') if x.strip()]

        self.set_checker_controls(False)
        self.checker_cancel_event.clear()
        self.checker_progress['value'] = 0

        thread = threading.Thread(target=self._checker_runner, args=(identifiers, session_name))
        thread.daemon = True
        thread.start()

    def stop_checker(self):
        self.checker_cancel_event.set()
        if self.checker_instance:
            self.checker_instance.cancel()

    def _checker_runner(self, identifiers, session_name):
        async def progress_callback(current, total, identifier):
            percent = (current / total) * 100
            self.after(0, self._update_checker_progress, percent, f"Checking {identifier}")

        async def runner():
            try:
                self.checker_instance = Checker(self.config)
                results = await self.checker_instance.run(identifiers, session_name, progress_callback)
                self.after(0, self._checker_finished, results)
            except Exception as e:
                self.after(0, self.log_checker, f"Error: {e}", True)
                self.after(0, self.set_checker_controls, True)

        run_asyncio_task(runner())

    def _update_checker_progress(self, percent, status_text):
        self.checker_progress['value'] = percent
        self.status_bar.config(text=f"Checker: {status_text}")

    def _checker_finished(self, results):
        self.set_checker_controls(True)
        self.checker_progress['value'] = 100
        self.status_bar.config(text="Checker: Completed")
        
        valid_count = sum(1 for r in results.values() if r['status'] == 'Valid')
        self.log_checker(f"Checker completed: {valid_count}/{len(results)} valid accounts")

    def start_forwarder(self):
        listener_session = self.forwarder_listener_combobox.get()
        sender_sessions = self.forwarder_senders_entry.get().strip()
        source = self.forwarder_source_entry.get().strip()
        targets = self.forwarder_targets_entry.get().strip()
        
        if not all([listener_session, sender_sessions, source, targets]):
            messagebox.showerror("Error", "Please fill all required fields")
            return

        sender_sessions = [x.strip() for x in sender_sessions.split(',') if x.strip()]
        targets = [x.strip() for x in targets.split(',') if x.strip()]
        
        self.set_forwarder_controls(False)
        self.forwarder_cancel_event.clear()
        
        thread = threading.Thread(target=self._forwarder_runner, args=(
            listener_session, sender_sessions, source, targets
        ))
        thread.daemon = True
        thread.start()

    def stop_forwarder(self):
        self.forwarder_cancel_event.set()
        if self.forwarder_instance:
            run_asyncio_task(self.forwarder_instance.stop())

    def _forwarder_runner(self, listener_session, sender_sessions, source, targets):
        async def runner():
            try:
                self.forwarder_instance = Forwarder(
                    self.config, listener_session, sender_sessions, source, targets,
                    False, 2.0, 5.0, self.forwarder_cancel_event
                )
                await self.forwarder_instance.start()
            except Exception as e:
                self.after(0, self.log_forwarder, f"Error: {e}", True)
            finally:
                self.after(0, self.set_forwarder_controls, True)

        run_asyncio_task(runner())

    def _update_leaver_mode(self):
        """Update the leaver tab UI based on selected mode"""
        mode = self.leaver_mode_var.get()
        
        # Hide all frames first
        self.leaver_targets_frame.pack_forget()
        self.leaver_inactive_frame.pack_forget()
        
        # Show relevant frames based on mode
        if mode == "specific":
            self.leaver_targets_frame.pack(fill='x', pady=5)
        elif mode == "inactive":
            self.leaver_inactive_frame.pack(fill='x', pady=5)
    
    def start_leaver(self):
        session_name = self.leaver_session_combobox.get()
        mode = self.leaver_mode_var.get()
        
        if not session_name:
            messagebox.showerror("Error", "Please select a session")
            return

        targets = None
        if mode == "specific":
            targets_text = self.leaver_targets_entry.get().strip()
            if not targets_text:
                messagebox.showerror("Error", "Please enter targets for specific mode")
                return
            targets = [t.strip() for t in targets_text.split(',') if t.strip()]
        
        days_threshold = 30
        if mode == "inactive":
            try:
                days_threshold = int(self.leaver_days_var.get())
                if days_threshold <= 0:
                    raise ValueError("Days threshold must be positive")
            except ValueError:
                messagebox.showerror("Error", "Please enter a valid number of days")
                return
        
        self.set_leaver_controls(False)
        self.leaver_cancel_event.clear()
        
        thread = threading.Thread(target=self._leaver_runner, args=(session_name, targets, mode, days_threshold))
        thread.daemon = True
        thread.start()

    def stop_leaver(self):
        self.leaver_cancel_event.set()
        if self.leaver_instance:
            run_asyncio_task(self.leaver_instance.stop())

    def _leaver_runner(self, session_name, targets, mode, days_threshold):
        async def runner():
            try:
                self.leaver_instance = Leaver(self.config, session_name, self.leaver_cancel_event)
                await self.leaver_instance.start(targets, mode, days_threshold)
            except Exception as e:
                self.after(0, self.log_leaver, f"Error: {e}", True)
            finally:
                self.after(0, self.set_leaver_controls, True)

        run_asyncio_task(runner())

    def clear_checker_log(self):
        self.checker_log.delete(1.0, tk.END)

    def clear_forwarder_log(self):
        self.forwarder_log.delete(1.0, tk.END)

    def clear_leaver_log(self):
        self.leaver_log.delete(1.0, tk.END)

    def open_checker_results(self):
        results_dir = self.config.get("checker", {}).get("output_folder", "output/checker")
        if os.path.exists(results_dir):
            os.startfile(results_dir)
        else:
            messagebox.showinfo("Info", "No results directory found")

    def open_forwarder_results(self):
        results_dir = self.config.get("output_dir", "output") + "/forwarder"
        if os.path.exists(results_dir):
            os.startfile(results_dir)
        else:
            messagebox.showinfo("Info", "No results directory found")

    def open_leaver_results(self):
        results_dir = self.config.get("output_dir", "output") + "/leaver"
        if os.path.exists(results_dir):
            os.startfile(results_dir)
        else:
            messagebox.showinfo("Info", "No results directory found")

    def reload_sessions(self):
        sessions = self.session_manager.get_session_files()
        
        # Update all session comboboxes
        self.checker_session_combobox['values'] = sessions
        self.forwarder_listener_combobox['values'] = sessions
        self.leaver_session_combobox['values'] = sessions
        
        # Update sessions listbox with status indicators
        self.sessions_listbox.delete(0, tk.END)
        for session in sessions:
            # Add status indicator to session name
            self.sessions_listbox.insert(tk.END, f"📱 {session}")
        
        # Clear session details if no sessions
        if not sessions:
            self.clear_session_details()

    def on_session_selected(self, event):
        selection = self.sessions_listbox.curselection()
        if selection:
            session_name = self.sessions_listbox.get(selection[0]).replace("📱 ", "").replace("❌ ", "").replace("✅ ", "")
            self.selected_session = session_name
            self.show_session_info(session_name)
            self.enable_session_actions(True)
        else:
            self.selected_session = None
            self.enable_session_actions(False)

    def on_session_double_click(self, event):
        """Handle double-click on session"""
        self.on_session_selected(event)

    def show_session_info(self, session_name):
        def get_info_sync():
            try:
                # Run validation in a separate thread to avoid blocking GUI
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_asyncio_task, self.session_manager.validate_session(session_name))
                    info = future.result(timeout=30)  # 30 second timeout
                    
                    if info is None:
                        info = {"status": "error", "error": "Validation timed out or failed"}
                    
                    self.after(0, self._display_session_info, session_name, info)
            except Exception as e:
                self.after(0, self._display_session_info, session_name, {"status": "error", "error": str(e)})

        # Run in a separate thread to avoid blocking the GUI
        import threading
        thread = threading.Thread(target=get_info_sync, daemon=True)
        thread.start()

    def _display_session_info(self, session_name, info):
        """Display session information in the details panel"""
        self.session_info_text.config(state=tk.NORMAL)
        self.session_info_text.delete(1.0, tk.END)
        
        status = info.get('status', 'Unknown')
        status_emoji = "✅" if status == 'valid' else "❌" if status == 'error' else "⚠️"
        
        info_text = f"""Session Information
{'='*50}

Name: {session_name}
Status: {status_emoji} {status.upper()}

"""
        
        if status == 'valid':
            user_info = info.get('user', {})
            info_text += f"""User Details:
• User ID: {user_info.get('user_id', 'N/A')}
• Username: @{user_info.get('username', 'N/A')}
• First Name: {user_info.get('first_name', 'N/A')}
• Last Name: {user_info.get('last_name', 'N/A')}
• Phone: {user_info.get('phone', 'N/A')}
"""
        elif status == 'not_authorized':
            info_text += "This session is not authorized.\n"
            info_text += "You can try to resend verification code or delete and recreate it."
        elif status == 'error':
            info_text += f"Error: {info.get('error', 'Unknown error')}\n\n"
            info_text += "This session may be invalid or expired.\n"
            info_text += "You can try to delete and recreate it."
        elif status == '2fa_required':
            info_text += "2FA password is required for this session.\n"
            info_text += "Please use the 'Resend Verification Code' button."
        elif status == 'flood_wait':
            info_text += f"Flood wait: {info.get('error', 'Unknown duration')}\n"
            info_text += "Please wait before trying again."
        
        self.session_info_text.insert(tk.END, info_text)
        self.session_info_text.config(state=tk.DISABLED)

    def clear_session_details(self):
        """Clear the session details panel"""
        self.session_info_text.config(state=tk.NORMAL)
        self.session_info_text.delete(1.0, tk.END)
        self.session_info_text.insert(tk.END, "No session selected")
        self.session_info_text.config(state=tk.DISABLED)

    def enable_session_actions(self, enabled):
        """Enable or disable session action buttons"""
        state = 'normal' if enabled else 'disabled'
        self.resend_code_btn.config(state=state)
        self.rename_session_btn.config(state=state)
        self.validate_session_btn.config(state=state)

    def add_session(self):
        phone = simpledialog.askstring("Add Session", "Enter phone number (with country code):")
        if phone:
            async def create_session():
                try:
                    result = await self.session_manager.create_new_session(phone)
                    if result['status'] == 'code_sent':
                        code = simpledialog.askstring("Verification", "Enter the code sent to your phone:")
                        if code:
                            # Pass the phone_code_hash from the previous step
                            phone_code_hash = result.get('phone_code_hash')
                            result = await self.session_manager.verify_code(
                                result['session_name'], phone, code, phone_code_hash
                            )
                            if result['status'] == 'success':
                                self.after(0, messagebox.showinfo, "Success", f"Session created: {result['session_name']}")
                                self.after(0, self.reload_sessions)
                            elif result['status'] == '2fa_required':
                                password = simpledialog.askstring("2FA Password", "Enter your 2FA password:", show="*")
                                if password:
                                    result = await self.session_manager.verify_code(
                                        result['session_name'], phone, code, phone_code_hash, password
                                    )
                                    if result['status'] == 'success':
                                        self.after(0, messagebox.showinfo, "Success", f"Session created: {result['session_name']}")
                                        self.after(0, self.reload_sessions)
                                    else:
                                        self.after(0, messagebox.showerror, "Error", f"Failed to verify 2FA: {result.get('error', 'Unknown error')}")
                                else:
                                    self.after(0, messagebox.showwarning, "Cancelled", "2FA verification cancelled")
                            else:
                                self.after(0, messagebox.showerror, "Error", f"Failed to verify code: {result.get('error', 'Unknown error')}")
                        else:
                            self.after(0, messagebox.showwarning, "Cancelled", "Code verification cancelled")
                    elif result['status'] == 'authorized':
                        self.after(0, messagebox.showinfo, "Success", f"Session already authorized: {result['session_name']}")
                        self.after(0, self.reload_sessions)
                    else:
                        self.after(0, messagebox.showerror, "Error", f"Failed to create session: {result.get('error', 'Unknown error')}")
                except Exception as e:
                    self.after(0, messagebox.showerror, "Error", f"Error creating session: {e}")

            run_asyncio_task(create_session())

    def delete_session(self):
        """Delete the selected session"""
        if not self.selected_session:
            messagebox.showwarning("Warning", "Please select a session to delete")
            return
        
        if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete session '{self.selected_session}'?"):
            try:
                # Delete session file
                session_file = os.path.join(self.sessions_dir, f"{self.selected_session}.session")
                if os.path.exists(session_file):
                    os.remove(session_file)
                    messagebox.showinfo("Success", f"Session '{self.selected_session}' deleted successfully")
                    self.reload_sessions()
                    self.selected_session = None
                    self.clear_session_details()
                    self.enable_session_actions(False)
                else:
                    messagebox.showerror("Error", f"Session file not found: {session_file}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to delete session: {e}")

    def resend_verification_code(self):
        """Resend verification code for the selected session"""
        if not self.selected_session:
            messagebox.showwarning("Warning", "Please select a session")
            return
        
        # Extract phone number from session name
        session_name = self.selected_session
        if session_name.startswith("session_"):
            phone = session_name.replace("session_", "")
            # Add + back if it was removed
            if not phone.startswith("+"):
                phone = "+" + phone
        else:
            phone = simpledialog.askstring("Phone Number", "Enter phone number for this session:")
            if not phone:
                return
        
        async def resend_code():
            try:
                result = await self.session_manager.resend_code_for_session(session_name, phone)
                if result['status'] == 'code_sent':
                    code = simpledialog.askstring("Verification", "Enter the new code sent to your phone:")
                    if code:
                        phone_code_hash = result.get('phone_code_hash')
                        result = await self.session_manager.verify_code(session_name, phone, code, phone_code_hash)
                        if result['status'] == 'success':
                            self.after(0, messagebox.showinfo, "Success", f"Session '{session_name}' verified successfully")
                            self.after(0, self.reload_sessions)
                            self.after(0, self.show_session_info, session_name)
                        elif result['status'] == '2fa_required':
                            password = simpledialog.askstring("2FA Password", "Enter your 2FA password:", show="*")
                            if password:
                                result = await self.session_manager.verify_code(session_name, phone, code, phone_code_hash, password)
                                if result['status'] == 'success':
                                    self.after(0, messagebox.showinfo, "Success", f"Session '{session_name}' verified successfully")
                                    self.after(0, self.reload_sessions)
                                    self.after(0, self.show_session_info, session_name)
                                else:
                                    self.after(0, messagebox.showerror, "Error", f"Failed to verify 2FA: {result.get('error', 'Unknown error')}")
                            else:
                                self.after(0, messagebox.showwarning, "Cancelled", "2FA verification cancelled")
                        else:
                            self.after(0, messagebox.showerror, "Error", f"Failed to verify code: {result.get('error', 'Unknown error')}")
                    else:
                        self.after(0, messagebox.showwarning, "Cancelled", "Code verification cancelled")
                elif result['status'] == 'authorized':
                    self.after(0, messagebox.showinfo, "Success", f"Session '{session_name}' is already authorized")
                    self.after(0, self.reload_sessions)
                    self.after(0, self.show_session_info, session_name)
                else:
                    self.after(0, messagebox.showerror, "Error", f"Failed to resend code: {result.get('error', 'Unknown error')}")
            except Exception as e:
                self.after(0, messagebox.showerror, "Error", f"Error resending code: {e}")

        run_asyncio_task(resend_code())

    def rename_session(self):
        """Rename the selected session"""
        if not self.selected_session:
            messagebox.showwarning("Warning", "Please select a session to rename")
            return

        new_name = simpledialog.askstring("Rename Session", f"Enter new name for session '{self.selected_session}':")
        if new_name and new_name != self.selected_session:
            try:
                old_file = os.path.join(self.sessions_dir, f"{self.selected_session}.session")
                new_file = os.path.join(self.sessions_dir, f"{new_name}.session")
                
                if os.path.exists(old_file):
                    if os.path.exists(new_file):
                        messagebox.showerror("Error", f"Session '{new_name}' already exists")
                        return
                    
                    os.rename(old_file, new_file)
                    messagebox.showinfo("Success", f"Session renamed from '{self.selected_session}' to '{new_name}'")
                    self.reload_sessions()
                    self.selected_session = new_name
                    self.show_session_info(new_name)
                else:
                    messagebox.showerror("Error", f"Session file not found: {old_file}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to rename session: {e}")

    def validate_selected_session(self):
        """Manually validate the selected session"""
        if not self.selected_session:
            messagebox.showwarning("Warning", "Please select a session to validate")
            return
        
        async def validate():
            try:
                info = await self.session_manager.validate_session(self.selected_session)
                self.after(0, self._display_session_info, self.selected_session, info)
                self.after(0, messagebox.showinfo, "Validation Complete", f"Session validation completed. Status: {info.get('status', 'Unknown')}")
            except Exception as e:
                self.after(0, messagebox.showerror, "Error", f"Failed to validate session: {e}")

        run_asyncio_task(validate())

    def load_config_file(self):
        filename = filedialog.askopenfilename(
            title="Load Configuration",
            filetypes=[("JSON files", "*.json"), ("YAML files", "*.yaml"), ("All files", "*.*")]
        )
        if filename:
            try:
                self.config = load_config(filename)
                messagebox.showinfo("Success", "Configuration loaded successfully")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load configuration: {e}")

    def save_config_file(self):
        filename = filedialog.asksaveasfilename(
            title="Save Configuration",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("YAML files", "*.yaml")]
        )
        if filename:
            try:
                save_config(self.config, filename)
                messagebox.showinfo("Success", "Configuration saved successfully")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save configuration: {e}")

    def save_settings(self):
        try:
            # Update config with UI values
            self.config["api_id"] = int(self.api_id_var.get())
            self.config["api_hash"] = self.api_hash_var.get()
            
            save_config(self.config)
            messagebox.showinfo("Success", "Settings saved successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {e}")

    def toggle_dark_mode(self):
        self.dark_mode = not self.dark_mode
        if self.dark_mode:
            self.style.theme_use('alt')
            self.configure(bg='#2b2b2b')
        else:
            self.style.theme_use('clam')
            self.configure(bg='SystemButtonFace')

    def show_about(self):
        about_text = """Telegram Automation Tool v2.0

A comprehensive tool for Telegram automation including:
• Account checking and validation
• Message forwarding with session rotation
• Group/channel management
• Session management with proxy support

Built with Telethon and Tkinter
"""
        messagebox.showinfo("About", about_text)

    def show_documentation(self):
        doc_text = """Documentation:

1. CHECKER MODULE:
   - Enter usernames or phone numbers (comma separated)
   - Select a session to use for checking
   - Results are saved in JSON and CSV formats

2. FORWARDER MODULE:
   - Listener session: Monitors source for new messages
   - Sender sessions: Accounts used to forward messages
   - Source: Group/channel to monitor
   - Targets: Groups/channels to forward to

3. LEAVER MODULE:
   - Specific: Leave specific groups/channels
   - All: Leave all groups and channels
   - Inactive: Leave groups inactive for specified days

4. SESSIONS:
   - Add new sessions with phone verification
   - View session status and information

5. SETTINGS:
   - Configure API credentials
   - Save/load configuration files
"""
        messagebox.showinfo("Documentation", doc_text)

    def on_close(self):
        # Stop any running operations
        self.checker_cancel_event.set()
        self.forwarder_cancel_event.set()
        self.leaver_cancel_event.set()
        
        if self.checker_instance:
            self.checker_instance.cancel()
        if self.forwarder_instance:
            run_asyncio_task(self.forwarder_instance.stop())
        if self.leaver_instance:
            run_asyncio_task(self.leaver_instance.stop())
        
        self.quit()
