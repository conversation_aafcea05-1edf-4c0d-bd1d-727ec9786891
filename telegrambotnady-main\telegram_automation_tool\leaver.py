# telegram_automation_tool/leaver.py

import os
import json
import asyncio
import csv
from datetime import datetime
from typing import List, Optional, Dict, Any
from telethon import functions
from telethon.errors import (
    FloodWaitError,
    ChatAdminRequiredError,
    UserNotParticipantError,
    ChannelPrivateError,
    SessionPasswordNeededError
)
from telegram_automation_tool.session_manager import SessionManager
from telegram_automation_tool.logger import setup_logger

class Leaver:
    def __init__(self, config, session_name, cancel_event=None, logger=None):
        self.config = config
        self.session_name = session_name
        self.cancel_event = cancel_event
        self.logger = logger or setup_logger('leaver', 'logs/leaver.log')
        self.session_manager = SessionManager(config)
        
        self._client = None
        self.results = {
            "left": 0, 
            "errors": 0,
            "skipped": 0,
            "start_time": None,
            "end_time": None,
            "details": []
        }
        self._running = False

    async def _get_all_dialogs(self):
        """Get all dialogs (groups and channels)"""
        try:
            dialogs = []
            async for dialog in self._client.iter_dialogs():
                if dialog.is_group or dialog.is_channel:
                    dialogs.append({
                        "id": dialog.id,
                        "title": dialog.title,
                        "username": dialog.entity.username,
                        "participants_count": getattr(dialog.entity, 'participants_count', 0),
                        "is_group": dialog.is_group,
                        "is_channel": dialog.is_channel
                    })
            return dialogs
        except Exception as e:
            self.logger.error(f"Error getting dialogs: {e}")
            return []

    async def _get_inactive_groups(self, days_threshold=30):
        """Get groups that haven't had activity in specified days"""
        try:
            inactive_groups = []
            async for dialog in self._client.iter_dialogs():
                if dialog.is_group or dialog.is_channel:
                    try:
                        # Get recent messages to check activity
                        messages = await self._client.get_messages(dialog, limit=1)
                        if messages:
                            last_message_date = messages[0].date
                            days_since_activity = (datetime.now() - last_message_date.replace(tzinfo=None)).days
                            
                            if days_since_activity > days_threshold:
                                inactive_groups.append({
                                    "id": dialog.id,
                                    "title": dialog.title,
                                    "username": dialog.entity.username,
                                    "days_inactive": days_since_activity,
                                    "last_message_date": last_message_date.isoformat()
                                })
                    except Exception as e:
                        self.logger.debug(f"Could not check activity for {dialog.title}: {e}")
                        
            return inactive_groups
        except Exception as e:
            self.logger.error(f"Error getting inactive groups: {e}")
            return []

    async def leave_target(self, target):
        """Leave a specific target"""
        try:
            try:
                entity = await self._client.get_entity(target)
            except ValueError as e:
                self.logger.warning(f"❌ Could not find entity for {target}: {e}")
                self.results["errors"] += 1
                self.results["details"].append({
                    "target": target,
                    "status": "not_found",
                    "error": f"Entity not found: {e}",
                    "timestamp": datetime.now().isoformat()
                })
                return False
            except Exception as e:
                self.logger.error(f"❌ Error resolving entity for {target}: {e}")
                self.results["errors"] += 1
                self.results["details"].append({
                    "target": target,
                    "status": "error",
                    "error": f"Error resolving entity: {e}",
                    "timestamp": datetime.now().isoformat()
                })
                return False
                
            if hasattr(entity, 'megagroup') and entity.megagroup:
                # Supergroup
                await self._client(functions.channels.LeaveChannelRequest(entity))
            elif hasattr(entity, 'gigagroup') and entity.gigagroup:
                # Broadcast group
                await self._client(functions.channels.LeaveChannelRequest(entity))
            elif hasattr(entity, 'broadcast') and entity.broadcast:
                # Channel
                await self._client(functions.channels.LeaveChannelRequest(entity))
            else:
                # Regular group
                await self._client(functions.messages.DeleteChatUserRequest(
                    chat_id=entity.id,
                    user_id='me'
                ))
                
            self.results["left"] += 1
            self.results["details"].append({
                "target": target,
                "title": getattr(entity, 'title', 'Unknown'),
                "username": getattr(entity, 'username', None),
                "status": "left",
                "timestamp": datetime.now().isoformat()
            })
            
            self.logger.info(f"✅ Left {getattr(entity, 'title', target)}")
            return True
            
        except FloodWaitError as e:
            self.logger.error(f"⏳ Flood wait for {e.seconds} seconds on {target}")
            self.results["errors"] += 1
            self.results["details"].append({
                "target": target,
                "status": "flood_wait",
                "error": f"Flood wait: {e.seconds}s",
                "timestamp": datetime.now().isoformat()
            })
            await asyncio.sleep(e.seconds)
            return False
            
        except (ChatAdminRequiredError, UserNotParticipantError, ChannelPrivateError) as e:
            self.logger.warning(f"⚠️ Cannot leave {target}: {e}")
            self.results["skipped"] += 1
            self.results["details"].append({
                "target": target,
                "status": "skipped",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Error leaving {target}: {e}")
            self.results["errors"] += 1
            self.results["details"].append({
                "target": target,
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            return False

    async def start(self, targets=None, mode="specific", days_threshold=30):
        """
        Start the leaver
        
        Args:
            targets: List of specific targets to leave (for mode="specific")
            mode: "specific", "all", or "inactive"
            days_threshold: Days threshold for inactive mode
        """
        if self._running:
            self.logger.warning("Leaver already running")
            return
            
        self.results["start_time"] = datetime.now().isoformat()
        self.logger.info(f"🚀 Starting leaver for session {self.session_name} in {mode} mode")
        
        try:
            self._client = self.session_manager.create_client(self.session_name)
            await self._client.start()
            self._running = True
            
            # Get targets based on mode
            if mode == "specific":
                if not targets:
                    self.logger.error("No targets specified for specific mode")
                    return
                leave_targets = targets
                self.logger.info(f"🎯 Leaving {len(leave_targets)} specific targets")
                
            elif mode == "all":
                dialogs = await self._get_all_dialogs()
                leave_targets = [dialog["id"] for dialog in dialogs]
                self.logger.info(f"🎯 Leaving all {len(leave_targets)} groups/channels")
                
            elif mode == "inactive":
                inactive_groups = await self._get_inactive_groups(days_threshold)
                leave_targets = [group["id"] for group in inactive_groups]
                self.logger.info(f"🎯 Leaving {len(leave_targets)} inactive groups (>{days_threshold} days)")
                
            else:
                self.logger.error(f"Invalid mode: {mode}")
                return

            # Leave targets
            delay = self.config.get("leaver", {}).get("delay", 3)
            
            for i, target in enumerate(leave_targets, 1):
                if self.cancel_event and self.cancel_event.is_set():
                    raise asyncio.CancelledError("Leaver task cancelled by user")
                    
                self.logger.info(f"Processing {i}/{len(leave_targets)}: {target}")
                
                await self.leave_target(target)
                
                # Add delay between leaves
                if i < len(leave_targets):
                    await asyncio.sleep(delay)
                    
        except asyncio.CancelledError:
            self.logger.info("Leaver task cancelled by user")
            raise
        except Exception as e:
            self.logger.error(f"Unhandled error in Leaver: {e}")
        finally:
            await self.stop()

    async def stop(self):
        """Stop the leaver"""
        if not self._running:
            return
            
        self.logger.info("🛑 Stopping leaver...")
        
        if self._client:
            try:
                await self._client.disconnect()
                self.logger.info("✅ Leaver client disconnected")
            except Exception as e:
                self.logger.error(f"Error disconnecting leaver client: {e}")
                
        self._running = False
        self.results["end_time"] = datetime.now().isoformat()
        self.logger.info("✅ Leaver stopped")

    def save_report(self):
        """Save detailed report"""
        out_dir = self.config.get("output_dir", "output") + "/leaver"
        os.makedirs(out_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_name = self.session_name.replace(" ", "_")
        
        # Save as JSON
        json_path = os.path.join(out_dir, f"{safe_name}_leaver_report_{timestamp}.json")
        with open(json_path, "w", encoding="utf-8") as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
            
        # Save as CSV
        csv_path = os.path.join(out_dir, f"{safe_name}_leaver_report_{timestamp}.csv")
        self._save_to_csv(csv_path)
        
        self.logger.info(f"📊 Leaver report saved to {json_path} and {csv_path}")
        return json_path

    def _save_to_csv(self, csv_path):
        """Save results to CSV format"""
        try:
            with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['target', 'title', 'username', 'status', 'error', 'timestamp']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for detail in self.results["details"]:
                    writer.writerow(detail)
        except Exception as e:
            self.logger.error(f"Error saving CSV: {e}")

    def get_stats(self):
        """Get current statistics"""
        return {
            "running": self._running,
            "left": self.results["left"],
            "errors": self.results["errors"],
            "skipped": self.results["skipped"]
        }
