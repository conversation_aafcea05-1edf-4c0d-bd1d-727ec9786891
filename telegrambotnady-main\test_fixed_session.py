#!/usr/bin/env python3
"""
Quick test to verify the fixed session creation
"""

import asyncio
from telegram_automation_tool.config import load_config
from telegram_automation_tool.session_manager import SessionManager

async def test_session_creation():
    """Test the fixed session creation"""
    print("🧪 Testing Fixed Session Creation")
    print("=" * 40)
    
    try:
        # Load config
        config = load_config()
        print(f"✅ Config loaded - API ID: {config.get('api_id', 'Not set')}")
        
        # Create session manager
        session_manager = SessionManager(config)
        
        # Test with a sample phone number (you'll need to replace this)
        test_phone = input("Enter phone number (with country code, e.g., +1234567890): ").strip()
        
        if not test_phone:
            print("❌ No phone number provided")
            return
        
        print(f"\n📱 Testing session creation for: {test_phone}")
        
        # Step 1: Create new session
        result = await session_manager.create_new_session(test_phone)
        print(f"Step 1 Result: {result['status']}")
        
        if result['status'] == 'code_sent':
            session_name = result['session_name']
            phone_code_hash = result.get('phone_code_hash')
            print(f"✅ Code sent successfully")
            print(f"📁 Session name: {session_name}")
            print(f"🔑 Phone code hash: {phone_code_hash[:20] if phone_code_hash else 'None'}...")
            
            # Step 2: Get verification code
            code = input("Enter the verification code sent to your phone: ").strip()
            
            if code:
                # Step 3: Verify code with phone_code_hash
                result = await session_manager.verify_code(session_name, test_phone, code, phone_code_hash)
                print(f"Step 3 Result: {result['status']}")
                
                if result['status'] == 'success':
                    print("🎉 Session created successfully!")
                    print(f"User: {result['user']['first_name']} {result['user']['last_name']}")
                    print(f"Username: @{result['user']['username']}")
                    print(f"User ID: {result['user']['id']}")
                    
                elif result['status'] == '2fa_required':
                    print("🔐 2FA required - this is working correctly!")
                    
                else:
                    print(f"❌ Code verification failed: {result.get('error')}")
            else:
                print("❌ No verification code provided")
                
        elif result['status'] == 'authorized':
            print("✅ Session already authorized!")
            
        else:
            print(f"❌ Session creation failed: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_session_creation())
