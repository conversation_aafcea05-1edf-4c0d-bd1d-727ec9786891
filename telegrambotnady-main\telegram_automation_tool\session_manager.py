# telegram_automation_tool/session_manager.py

import os
import asyncio
import random
from telethon import TelegramClient
from telethon.sessions import StringSession
from telethon.errors import SessionPasswordNeededError, FloodWaitError
from telegram_automation_tool.logger import setup_logger

class SessionManager:
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger('session_manager', 'logs/session_manager.log')
        self.sessions_dir = config.get("sessions_dir", "sessions")
        os.makedirs(self.sessions_dir, exist_ok=True)
        
        # Anti-ban settings
        self.rate_limits = {
            'joins_per_hour': 20,
            'forwards_per_hour': 50,
            'messages_per_hour': 100
        }
        
    def get_proxy_config(self, session_name=None):
        """Get proxy configuration, optionally rotating for different sessions"""
        proxy_config = self.config.get("proxy", {})
        if not proxy_config.get("enabled", False):
            return None
            
        # If multiple proxies are configured, rotate them
        proxies = proxy_config.get("proxies", [proxy_config])
        if session_name and len(proxies) > 1:
            # Use session name to deterministically select proxy
            proxy_index = hash(session_name) % len(proxies)
            proxy = proxies[proxy_index]
        else:
            proxy = proxies[0]
            
        return {
            'proxy_type': proxy.get("type", "socks5"),
            'addr': proxy.get("host", "127.0.0.1"),
            'port': proxy.get("port", 9050),
            'username': proxy.get("username", ""),
            'password': proxy.get("password", ""),
            'rdns': True
        }
    
    def create_client(self, session_name, config=None):
        """Create a Telegram client with proxy support and anti-ban logic"""
        if config is None:
            config = self.config
            
        session_path = os.path.join(self.sessions_dir, f"{session_name}.session")
        api_id = config.get("api_id")
        api_hash = config.get("api_hash")
        
        if not api_id or not api_hash:
            raise ValueError("API ID and API Hash must be configured")
        
        proxy_config = self.get_proxy_config(session_name)
        
        client = TelegramClient(
            session_path,
            api_id,
            api_hash,
            proxy=proxy_config,
            device_model="Desktop",
            system_version="Windows 10",
            app_version="1.0",
            lang_code="en",
            system_lang_code="en-US"
        )
        
        return client
    
    async def validate_session(self, session_name):
        """Validate if a session is working and get its status"""
        client = None
        try:
            client = self.create_client(session_name)
            await client.connect()
            
            if not await client.is_user_authorized():
                await client.disconnect()
                return {"status": "not_authorized", "error": "Session not authorized"}
            
            # Try to get self info
            me = await client.get_me()
            await client.disconnect()
            
            return {
                "status": "valid",
                "user": {
                    "user_id": me.id,
                    "username": me.username,
                    "first_name": me.first_name,
                    "last_name": me.last_name,
                    "phone": me.phone
                }
            }
            
        except SessionPasswordNeededError:
            if client:
                await client.disconnect()
            return {"status": "2fa_required", "error": "2FA password required"}
        except FloodWaitError as e:
            if client:
                await client.disconnect()
            return {"status": "flood_wait", "error": f"Flood wait: {e.seconds} seconds"}
        except Exception as e:
            if client:
                try:
                    await client.disconnect()
                except:
                    pass
            return {"status": "error", "error": str(e)}
    
    def get_session_files(self):
        """Get list of all session files"""
        sessions = []
        if os.path.exists(self.sessions_dir):
            for file in os.listdir(self.sessions_dir):
                if file.endswith('.session'):
                    session_name = file[:-8]  # Remove .session extension
                    sessions.append(session_name)
        return sessions
    
    async def create_new_session(self, phone_number, password=None):
        """Create a new session with phone number"""
        session_name = f"session_{phone_number.replace('+', '').replace(' ', '')}"
        client = self.create_client(session_name)
        
        try:
            await client.connect()
            
            if not await client.is_user_authorized():
                # Send code and get phone_code_hash
                sent_code = await client.send_code_request(phone_number)
                return {
                    "status": "code_sent", 
                    "session_name": session_name,
                    "phone_code_hash": sent_code.phone_code_hash
                }
            
            # Already authorized
            me = await client.get_me()
            await client.disconnect()
            return {
                "status": "authorized",
                "session_name": session_name,
                "user": {
                    "id": me.id,
                    "username": me.username,
                    "first_name": me.first_name,
                    "last_name": me.last_name,
                    "phone": me.phone
                }
            }
            
        except Exception as e:
            await client.disconnect()
            return {"status": "error", "error": str(e)}
    
    async def verify_code(self, session_name, phone_number, code, phone_code_hash=None, password=None):
        """Verify the code sent to phone number"""
        client = self.create_client(session_name)
        
        try:
            await client.connect()
            
            # Sign in with code and phone_code_hash
            if phone_code_hash:
                await client.sign_in(phone_number, code, phone_code_hash=phone_code_hash)
            else:
                await client.sign_in(phone_number, code)
            
            # Check if 2FA is required
            if password:
                await client.sign_in(password=password)
            
            me = await client.get_me()
            await client.disconnect()
            
            return {
                "status": "success",
                "session_name": session_name,
                "user": {
                    "id": me.id,
                    "username": me.username,
                    "first_name": me.first_name,
                    "last_name": me.last_name,
                    "phone": me.phone
                }
            }
            
        except SessionPasswordNeededError:
            await client.disconnect()
            return {"status": "2fa_required", "session_name": session_name}
        except Exception as e:
            await client.disconnect()
            return {"status": "error", "error": str(e)}

    async def resend_code_for_session(self, session_name, phone_number):
        """Resend verification code for an existing session"""
        client = self.create_client(session_name)
        
        try:
            await client.connect()
            
            # Send code request
            sent_code = await client.send_code_request(phone_number)
            await client.disconnect()
            
            return {
                "status": "code_sent",
                "session_name": session_name,
                "phone_code_hash": sent_code.phone_code_hash
            }
            
        except Exception as e:
            await client.disconnect()
            return {"status": "error", "error": str(e)}

# Backward compatibility
def create_client(session_name, config):
    manager = SessionManager(config)
    return manager.create_client(session_name, config)
