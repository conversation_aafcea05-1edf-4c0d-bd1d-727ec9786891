#!/usr/bin/env python3
"""
Test script to verify Telegram Automation Tool installation
"""

import sys
import os
import importlib

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    required_modules = [
        'telethon',
        'tkinter',
        'asyncio',
        'json',
        'csv',
        'datetime',
        'threading',
        'os',
        'shutil'
    ]
    
    failed_imports = []
    
    for module in required_modules:
        try:
            importlib.import_module(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")
            failed_imports.append(module)
    
    return len(failed_imports) == 0

def test_local_modules():
    """Test if local modules can be imported"""
    print("\nTesting local modules...")
    
    local_modules = [
        'telegram_automation_tool.config',
        'telegram_automation_tool.logger',
        'telegram_automation_tool.session_manager',
        'telegram_automation_tool.checker',
        'telegram_automation_tool.forwarder',
        'telegram_automation_tool.leaver'
    ]
    
    failed_imports = []
    
    for module in local_modules:
        try:
            importlib.import_module(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")
            failed_imports.append(module)
    
    return len(failed_imports) == 0

def test_directories():
    """Test if required directories exist or can be created"""
    print("\nTesting directories...")
    
    directories = [
        'sessions',
        'logs',
        'output',
        'temp',
        'output/checker',
        'output/forwarder',
        'output/leaver'
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ {directory}")
        except Exception as e:
            print(f"❌ {directory}: {e}")

def test_config():
    """Test if configuration can be loaded"""
    print("\nTesting configuration...")
    
    try:
        from telegram_automation_tool.config import load_config
        config = load_config()
        print("✅ Configuration loaded successfully")
        print(f"   API ID: {config.get('api_id', 'Not set')}")
        print(f"   Sessions dir: {config.get('sessions_dir', 'Not set')}")
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def main():
    """Run all tests"""
    print("Telegram Automation Tool - Installation Test")
    print("=" * 50)
    
    # Test Python version
    print(f"Python version: {sys.version}")
    if sys.version_info < (3, 7):
        print("❌ Python 3.7+ is required")
        return False
    else:
        print("✅ Python version is compatible")
    
    # Run tests
    imports_ok = test_imports()
    local_modules_ok = test_local_modules()
    test_directories()
    config_ok = test_config()
    
    print("\n" + "=" * 50)
    print("Test Results:")
    print(f"Imports: {'✅ PASS' if imports_ok else '❌ FAIL'}")
    print(f"Local modules: {'✅ PASS' if local_modules_ok else '❌ FAIL'}")
    print(f"Configuration: {'✅ PASS' if config_ok else '❌ FAIL'}")
    
    if all([imports_ok, local_modules_ok, config_ok]):
        print("\n🎉 All tests passed! The tool is ready to use.")
        print("\nNext steps:")
        print("1. Update config.json with your API credentials")
        print("2. Run 'python main.py' to start the application")
        return True
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        print("\nTroubleshooting:")
        print("1. Install missing dependencies: pip install -r requirements.txt")
        print("2. Check if all files are in the correct locations")
        print("3. Verify Python version is 3.7+")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
