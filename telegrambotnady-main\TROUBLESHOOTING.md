# Troubleshooting Guide

## Common Issues and Solutions

### 1. **'dict' object is not callable Error**
**Problem**: This error occurs when there's a naming conflict between variables and built-in functions.

**Solution**: ✅ **FIXED** - Updated the config.py file to avoid the `validate` function name conflict.

### 2. **No module named 'pip' Error**
**Problem**: pip is not installed or not in PATH.

**Solutions**:
```bash
# Reinstall pip
python -m ensurepip --default-pip

# Use python -m pip instead of pip directly
python -m pip install -r requirements.txt
```

### 3. **tgcrypto Build Error**
**Problem**: tgcrypto requires Microsoft Visual C++ Build Tools.

**Solutions**:
- **Option 1**: Install pre-compiled version: `pip install --only-binary=all tgcrypto`
- **Option 2**: Comment out tgcrypto in requirements.txt (optional for performance)
- **Option 3**: Install Visual C++ Build Tools from Microsoft

### 4. **Import Errors**
**Problem**: Missing dependencies or incorrect imports.

**Solution**:
```bash
# Reinstall all dependencies
python -m pip install -r requirements.txt

# Test installation
python test_installation.py
```

### 5. **Configuration Errors**
**Problem**: Invalid config.json or missing API credentials.

**Solutions**:
- Check config.json format
- Get API credentials from [my.telegram.org](https://my.telegram.org)
- Use config.example.json as template

### 6. **Session Authorization Errors**
**Problem**: Session files are invalid or expired.

**Solutions**:
- Delete old session files
- Create new sessions through the GUI
- Check phone number format (+**********)
- Verify 2FA password if enabled

### 7. **Flood Wait Errors**
**Problem**: Too many requests to Telegram API.

**Solutions**:
- Increase delays between operations
- Use fewer sessions simultaneously
- Wait for flood wait to expire
- Use proxy rotation

### 8. **Proxy Connection Issues**
**Problem**: Proxy server is down or misconfigured.

**Solutions**:
- Verify proxy credentials
- Check proxy server status
- Try different proxy types (HTTP/SOCKS5)
- Test proxy connection manually

## Quick Fixes

### Reset Everything
```bash
# Remove all session files
rm -rf sessions/*

# Remove logs
rm -rf logs/*

# Remove output files
rm -rf output/*

# Reinstall dependencies
python -m pip install -r requirements.txt --force-reinstall
```

### Test Installation
```bash
# Run comprehensive test
python test_installation.py

# Run quick test
python quick_test.py
```

### Start Fresh
```bash
# Copy example config
cp config.example.json config.json

# Edit config.json with your API credentials
# Then run the application
python main.py
```

## Getting Help

1. **Check Logs**: Look in the `logs/` directory for detailed error messages
2. **Run Tests**: Use `python test_installation.py` to diagnose issues
3. **Check Configuration**: Verify your config.json is valid
4. **Update Dependencies**: Ensure all packages are up to date

## Common Commands

```bash
# Install dependencies
python -m pip install -r requirements.txt

# Test installation
python test_installation.py

# Quick test
python quick_test.py

# Start application
python main.py

# Start with batch file (Windows)
start.bat
```

## API Credentials

1. Visit [my.telegram.org](https://my.telegram.org)
2. Log in with your phone number
3. Go to "API development tools"
4. Create a new application
5. Copy API ID and API Hash
6. Update config.json

## Session Management

1. **Add Session**: Use the Sessions tab in the GUI
2. **Phone Format**: Use international format (+**********)
3. **2FA**: Enter password if two-factor authentication is enabled
4. **Verification**: Enter the code sent to your phone

## Performance Tips

1. **Use Proxies**: Distribute load across multiple IPs
2. **Session Rotation**: Use multiple accounts
3. **Rate Limiting**: Respect Telegram's limits
4. **Delays**: Add delays between operations
5. **Monitoring**: Check logs for errors

---

**Note**: Always use this tool responsibly and in compliance with Telegram's terms of service.
